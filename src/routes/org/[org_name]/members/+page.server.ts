import { redirect } from '@sveltejs/kit';
import { fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals, depends }) => {
	depends('org:members');

	const { org_name } = params;
	const { supabase } = locals;

	// Get organization by name
	const { data: organization, error: orgError } = await supabase
		.rpc('get_organization_by_name', { org_name_param: org_name })
		.maybeSingle();

	if (orgError || !organization) {
		console.error('Error fetching organization:', orgError);
		throw redirect(303, '/');
	}

	// Get organization members with their profiles
	const { data: members, error: membersError } = await supabase.rpc('get_organization_members', {
		_org_name: params.org_name,
	});

	if (membersError) {
		console.error('Error fetching organization members:', membersError);
		return {
			organization,
			members: [],
		};
	}

	console.log({ members });

	// Get pending invites for this organization
	const { data: invites, error: invitesError } = await supabase
		.from('invite')
		.select(
			`
			invite_id,
			role,
			invitee_email,
			created_at,
			expires_at,
			profile:inviter_id (
				user_id,
				full_name,
				email
			)
		`,
		)
		.eq('resource_type', 'organization')
		.eq('resource_id', organization.org_id)
		.eq('status', 'pending')
		.order('created_at', { ascending: false });

	if (invitesError) {
		console.error('Error fetching organization invites:', invitesError);
		return {
			organization,
			members,
			invites: [],
			isAdmin: false,
		};
	}

	// Check if current user is admin (to enable/disable member management UI)
	const { data: isAdmin } = await supabase.rpc('current_user_has_entity_role', {
		entity_type_param: 'organization',
		entity_id_param: organization.org_id,
		min_role_param: 'admin',
	});

	return {
		organization,
		members,
		invites: invites || [],
		isAdmin,
	};
};

export const actions: Actions = {
	remove: async ({ request, locals, params }) => {
		const formData = await request.formData();
		const memberId = formData.get('memberId')?.toString();
		const { org_name } = params;
		const { supabase } = locals;

		if (!memberId) {
			return fail(400, { message: 'Missing member ID' });
		}

		// Get organization by name
		const { data: organization, error: orgError } = await supabase
			.rpc('get_organization_by_name', { org_name_param: org_name })
			.maybeSingle();

		if (orgError || !organization) {
			return fail(404, { message: 'Organization not found' });
		}

		// Check if current user is admin
		const { data: isAdmin } = await supabase.rpc('current_user_has_entity_role', {
			entity_type_param: 'organization',
			entity_id_param: organization.org_id,
			min_role_param: 'admin',
		});

		if (!isAdmin) {
			return fail(403, { message: 'Not authorized' });
		}

		// Remove the member
		const { error: removeError } = await supabase
			.from('membership')
			.delete()
			.eq('membership_id', memberId);

		if (removeError) {
			return fail(500, { message: 'Failed to remove member' });
		}

		return { success: true };
	},

	cancelInvite: async ({ request, locals, params }) => {
		const formData = await request.formData();
		const inviteId = formData.get('inviteId')?.toString();
		const { org_name } = params;
		const { supabase } = locals;
		const userId = locals.user?.id;

		if (!inviteId) {
			return fail(400, { message: 'Missing invite ID' });
		}

		if (!userId) {
			return fail(401, { message: 'Not authenticated' });
		}

		// Get organization by name
		const { data: organization, error: orgError } = await supabase
			.rpc('get_organization_by_name', { org_name_param: org_name })
			.maybeSingle();

		if (orgError || !organization) {
			return fail(404, { message: 'Organization not found' });
		}

		// Check if current user is admin
		const { data: isAdmin } = await supabase.rpc('current_user_has_entity_role', {
			entity_type_param: 'organization',
			entity_id_param: organization.org_id,
			min_role_param: 'admin',
		});

		if (!isAdmin) {
			return fail(403, { message: 'Not authorized' });
		}

		// Update the invite status to 'revoked'
		const { error: updateError } = await supabase
			.from('invite')
			.update({
				status: 'revoked',
				updated_at: new Date().toISOString(),
				updated_by: userId,
			})
			.eq('invite_id', inviteId)
			.eq('resource_type', 'organization')
			.eq('resource_id', organization.org_id);

		if (updateError) {
			console.error('Error revoking invitation:', updateError);
			return fail(500, { message: 'Failed to cancel invitation' });
		}

		return { success: true };
	},
};
