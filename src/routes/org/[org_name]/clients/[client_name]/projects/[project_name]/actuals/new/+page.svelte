<script lang="ts">
	import type { PageProps } from './$types';
	import NewInvoice from '$lib/components/forms/invoice/NewInvoice.svelte';

	const { data }: PageProps = $props();
</script>

<div class="container mx-auto py-6">
	<NewInvoice
		data={{
			form: data.form,
			newVendorForm: data.newVendorForm,
			vendors: data.vendors,
			purchaseOrders: data.purchaseOrders,
			project: data.project,
		}}
	/>
</div>
