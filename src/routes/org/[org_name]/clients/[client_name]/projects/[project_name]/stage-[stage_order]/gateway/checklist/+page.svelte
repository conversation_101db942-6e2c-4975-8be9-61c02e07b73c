<script lang="ts">
	import type { PageProps } from './$types';
	import * as Form from '$lib/components/ui/form';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import { Constants } from '$lib/database.types';
	import { page } from '$app/state';

	const { data }: PageProps = $props();
	const { project, currentStage, checklistItems } = data;

	// Initialize Superforms with validation
	const checklistSuperForm = superForm(data.checklistForm, {
		dataType: 'json',
		resetForm: false,
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: checklistForm, enhance: checklistEnhance } = checklistSuperForm;
</script>

<div class="container mx-auto max-w-(--breakpoint-xl) py-8">
	<div class="mb-8 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">Gateway Checklist Items</h1>
			<p class="mt-1 text-slate-600">
				Manage checklist items for {currentStage?.name || 'Unknown Stage'}
			</p>
		</div>
		<!-- <div class="flex space-x-4"> -->
		<a
			href="/org/{page.params.org_name}/clients/{project.client
				.name}/projects/{project.name}/stage-{currentStage?.stage_order}/gateway/checklist/new"
		>
			<Button>Add New Checklist Item</Button>
		</a>
		<!-- <a
				href="/org/{page.params.org_name}/clients/{project.client
					.name}/projects/{project.name}/stage-{currentStage?.stage_order}/gateway"
			>
				<Button variant="outline">Back to Gateway</Button>
			</a>
		</div> -->
	</div>

	<div class="rounded-lg border bg-white p-6 shadow-xs">
		{#if checklistItems.length === 0}
			<div class="rounded-lg border bg-slate-50 p-8 text-center">
				<p class="text-slate-600">No checklist items defined for this stage.</p>
				<a
					href="/org/{page.params.org_name}/clients/{project.client
						.name}/projects/{project.name}/stage-{currentStage?.stage_order}/gateway/checklist/new"
				>
					<Button variant="outline" class="mt-4">Add Checklist Item</Button>
				</a>
			</div>
		{:else}
			<form method="POST" action="?/updateChecklist" class="space-y-6" use:checklistEnhance>
				{#each checklistItems as item, i (item.gateway_checklist_item_id)}
					<div class="rounded border bg-gray-50 p-6">
						<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
							<!-- Item Name -->
							<div class="md:col-span-2">
								<Form.Field form={checklistSuperForm} name="items[{i}].name">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label>Item Name</Form.Label>
											<Input {...props} bind:value={$checklistForm.items[i].name} required />
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</div>

							<!-- Status -->
							<div>
								<Form.Field form={checklistSuperForm} name="items[{i}].status">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label>Status</Form.Label>
											<Select.Root
												type="single"
												bind:value={$checklistForm.items[i].status}
												name={props.name}
											>
												<Select.Trigger {...props} class="w-full">
													{$checklistForm.items[i].status}
												</Select.Trigger>
												<Select.Content>
													{#each Constants.public.Enums.checklist_item_status as status (status)}
														<Select.Item value={status} label={status} />
													{/each}
												</Select.Content>
											</Select.Root>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</div>

							<!-- Description -->
							<div class="md:col-span-3">
								<Form.Field form={checklistSuperForm} name="items[{i}].description">
									<Form.Control>
										{#snippet children({ props })}
											<Form.Label>Description</Form.Label>
											<Textarea
												{...props}
												rows={3}
												bind:value={$checklistForm.items[i].description}
											/>
										{/snippet}
									</Form.Control>
									<Form.FieldErrors />
								</Form.Field>
							</div>
						</div>

						<input
							type="hidden"
							name="items[{i}].gateway_checklist_item_id"
							value={item.gateway_checklist_item_id}
						/>
					</div>
				{/each}

				<div class="flex justify-end">
					<Button type="submit">Save Changes</Button>
				</div>
			</form>
		{/if}
	</div>
</div>
