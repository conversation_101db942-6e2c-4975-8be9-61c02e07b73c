<script lang="ts">
	import type { PageProps } from './$types';
	import * as Form from '$lib/components/ui/form';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { superForm } from 'sveltekit-superforms';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/state';
	import SpinnerGapIcon from 'phosphor-svelte/lib/SpinnerGap';

	const { data }: PageProps = $props();
	const { project, currentStage } = data;

	// Initialize Superforms with validation
	const formHandler = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form, enhance, delayed, submitting } = formHandler;
</script>

<div class="container mx-auto max-w-(--breakpoint-xl) py-8">
	<div class="mb-8 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">Add New Gateway Checklist Item</h1>
			<p class="mt-1 text-slate-600">
				Create a new checklist item for {currentStage?.name || 'Unknown Stage'}
			</p>
		</div>
		<div>
			<a
				href="/org/{page.params.org_name}/clients/{project.client
					.name}/projects/{project.name}/stage-{currentStage?.stage_order}/gateway/checklist"
			>
				<Button variant="outline">Back to Checklist</Button>
			</a>
		</div>
	</div>

	<div class="rounded-lg border bg-white p-6 shadow-xs">
		<form method="POST" class="space-y-6" use:enhance>
			<!-- Hidden field for project_stage_id -->
			<input type="hidden" name="project_stage_id" value={currentStage.project_stage_id} />

			<!-- Item Name -->
			<Form.Field form={formHandler} name="name">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Item Name <span class="text-red-500">*</span></Form.Label>
						<Input
							{...props}
							bind:value={$form.name}
							required
							placeholder="Enter checklist item name"
						/>
					{/snippet}
				</Form.Control>
				<Form.Description>
					A clear, concise name for the checklist item (e.g., "Design Review Completed")
				</Form.Description>
				<Form.FieldErrors />
			</Form.Field>

			<!-- Description -->
			<Form.Field form={formHandler} name="description">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Description</Form.Label>
						<Textarea
							{...props}
							bind:value={$form.description}
							rows={4}
							placeholder="Enter a detailed description of what this checklist item involves"
						/>
					{/snippet}
				</Form.Control>
				<Form.Description>
					Provide additional context or requirements for completing this checklist item
				</Form.Description>
				<Form.FieldErrors />
			</Form.Field>

			<div class="flex justify-end pt-4">
				<Button type="submit" disabled={$submitting}>
					{#if $delayed}
						<SpinnerGapIcon class="text-primary size-4 animate-spin" />
					{/if}
					Create Checklist Item
				</Button>
			</div>
		</form>
	</div>
</div>
