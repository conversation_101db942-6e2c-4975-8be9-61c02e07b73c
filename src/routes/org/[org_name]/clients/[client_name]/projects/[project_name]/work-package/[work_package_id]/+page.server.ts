import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, locals }) => {
	await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_name, work_package_id } = params;
	requireProject();

	// Fetch the work package with all related information
	const { data: workPackage, error: workPackageError } = await supabase
		.from('work_package')
		.select(
			`
			*,
			project!inner(
				project_id,
				name,
				client!inner(
					name,
					organization!inner(name)
				)
			),
			wbs_library_item(
				wbs_library_item_id,
				code,
				description,
				cost_scope
			)
		`,
		)
		.eq('work_package_id', work_package_id)
		.eq('project.client.organization.name', org_name)
		.eq('project.client.name', client_name)
		.eq('project.name', project_name)
		.single();

	if (workPackageError || !workPackage) {
		console.error('Error fetching work package:', workPackageError);
		throw error(404, 'Work package not found');
	}

	// Fetch purchase orders that reference this work package
	const { data: purchaseOrders, error: purchaseOrdersError } = await supabase
		.from('purchase_order')
		.select(
			`
			purchase_order_id,
			po_number,
			description,
			original_amount,
			co_amount,
			vendor(name)
		`,
		)
		.eq('work_package_id', work_package_id);

	if (purchaseOrdersError) {
		console.error('Error fetching purchase orders:', purchaseOrdersError);
		throw error(500, 'Failed to fetch purchase orders');
	}

	return {
		workPackage,
		purchaseOrders: purchaseOrders || [],
	};
};

export const actions: Actions = {
	delete: async ({ params, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name, work_package_id } = params;

		// Get work package name for success message
		const { data: workPackageData, error: workPackageError } = await supabase
			.from('work_package')
			.select('name')
			.eq('work_package_id', work_package_id)
			.single();

		if (workPackageError || !workPackageData) {
			console.error('Error fetching work package for deletion:', workPackageError);
			return fail(404, {
				message: { type: 'error', text: 'Work package not found' },
			});
		}

		// Delete the work package
		const { error: deleteError } = await supabase
			.from('work_package')
			.delete()
			.eq('work_package_id', work_package_id);

		if (deleteError) {
			console.error('Error deleting work package:', deleteError);
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete work package' },
			});
		}

		throw redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_name)}/work-package`,
			{
				type: 'success',
				message: `Work package "${workPackageData.name}" deleted successfully`,
			},
			cookies,
		);
	},
};
