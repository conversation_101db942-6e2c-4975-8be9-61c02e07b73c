import { editProjectSchema } from '$lib/schemas/project';
import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	await requireUser();

	const { supabase } = locals;
	const { project_name, client_name, org_name } = params;

	// Combined query to fetch project with organization and client data in one query
	const { data: project, error } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization!inner(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (error || !project) {
		console.error('Error fetching project:', error);
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found.' },
			cookies,
		);
	}

	// Fetch available WBS libraries for the dropdown
	const { data: wbsLibraries } = await supabase
		.from('wbs_library')
		.select('wbs_library_id, name')
		.order('name');

	// Create a form using the edit project schema and pre-populate with project data
	const projectFormData = {
		name: project.name,
		description: project.description,
		wbs_library_id: project.wbs_library_id,
	};
	const form = await superValidate(projectFormData, zod(editProjectSchema));

	return {
		form,
		project,
		wbsLibraries: wbsLibraries || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name, org_name } = params;

		// Validate form data
		const form = await superValidate(request, zod(editProjectSchema));

		console.log({ form });

		if (!form.valid) {
			return fail(400, { form });
		}

		// Combined query to fetch project with organization and client data in one query
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('project_id, client!inner(name, client_id, organization!inner(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project ID:', projectError);
			return message(form, { type: 'error', text: 'Project not found.' }, { status: 404 });
		}

		// Check if user has permission to edit this project - moved after main data fetching
		const { data: hasPermission, error: permissionError } = await supabase.rpc(
			'can_modify_project',
			{
				project_id_param: projectData.project_id,
			},
		);

		if (permissionError || !hasPermission) {
			console.error('Permission error:', permissionError);
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project.' },
				{ status: 403 },
			);
		}

		// Update the project
		const { error: updateError } = await supabase
			.from('project')
			.update(form.data)
			.eq('project_id', projectData.project_id);

		if (updateError) {
			console.error('Error updating project:', updateError);
			return message(form, { type: 'error', text: 'Error updating project.' }, { status: 400 });
		}

		if (form.data.name !== project_name) {
			return redirect(
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(form.data.name)}/edit`,
				{ type: 'success', message: 'Project updated successfully.' },
				cookies,
			);
		}

		return message(form, { type: 'success', text: 'Project updated successfully.' });
	},
};
