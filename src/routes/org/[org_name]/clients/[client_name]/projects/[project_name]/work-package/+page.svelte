<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import * as Dialog from '$lib/components/ui/dialog';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';

	const { data }: PageProps = $props();
	const { workPackages, project } = data;

	let deleteDialogOpen = $state(false);
	let workPackageToDelete = $state<string | null>(null);

	function handleEdit(workPackageId: string) {
		goto(
			`/org/${encodeURIComponent(data.org_name)}/clients/${encodeURIComponent(
				data.client_name,
			)}/projects/${encodeURIComponent(data.project_name)}/work-package/${workPackageId}/edit`,
		);
	}

	function handleView(workPackageId: string) {
		goto(
			`/org/${encodeURIComponent(data.org_name)}/clients/${encodeURIComponent(
				data.client_name,
			)}/projects/${encodeURIComponent(data.project_name)}/work-package/${workPackageId}`,
		);
	}

	function openDeleteDialog(workPackageId: string) {
		workPackageToDelete = workPackageId;
		deleteDialogOpen = true;
	}

	function confirmDelete() {
		// The form submission will be handled by the enhance action
		deleteDialogOpen = false;
	}

	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-GB', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	}
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-semibold">Work Packages</h1>
			<p class="text-muted-foreground mt-1">
				Manage work packages for {project.name}
			</p>
		</div>
		<Button
			href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
				data.client_name,
			)}/projects/{encodeURIComponent(data.project_name)}/work-package/new"
			class="gap-2"
		>
			<PlusIcon class="size-4" />
			New Work Package
		</Button>
	</div>

	{#if workPackages.length === 0}
		<div class="py-12 text-center">
			<p class="text-muted-foreground mb-4 text-lg">No work packages found</p>
			<Button
				href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
					data.client_name,
				)}/projects/{encodeURIComponent(data.project_name)}/work-package/new"
				class="gap-2"
			>
				<PlusIcon class="size-4" />
				Create your first work package
			</Button>
		</div>
	{:else}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Name</TableHead>
						<TableHead>Description</TableHead>
						<TableHead>Purchase Orders</TableHead>
						<TableHead>WBS Code</TableHead>
						<TableHead>Created</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each workPackages as workPackage (workPackage.work_package_id)}
						<TableRow>
							<TableCell class="font-medium">
								<button
									onclick={() => handleView(workPackage.work_package_id)}
									class="text-left hover:underline"
								>
									{workPackage.name}
								</button>
							</TableCell>
							<TableCell class="text-muted-foreground">
								{workPackage.description || '—'}
							</TableCell>
							<TableCell class="text-muted-foreground">
								{workPackage.purchase_order_count > 0
									? `${workPackage.purchase_order_count} PO${workPackage.purchase_order_count > 1 ? 's' : ''}`
									: '—'}
							</TableCell>
							<TableCell class="text-muted-foreground">
								{workPackage.wbs_code || '—'}
							</TableCell>
							<TableCell class="text-muted-foreground">
								{formatDate(workPackage.created_at)}
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="h-8 w-8 p-0">
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="h-4 w-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item onclick={() => handleView(workPackage.work_package_id)}>
											View
										</DropdownMenu.Item>
										<DropdownMenu.Item onclick={() => handleEdit(workPackage.work_package_id)}>
											Edit
										</DropdownMenu.Item>
										<DropdownMenu.Separator />
										<DropdownMenu.Item
											onclick={() => openDeleteDialog(workPackage.work_package_id)}
											class="text-destructive"
										>
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{/if}
</div>

<!-- Delete Confirmation Dialog -->
<Dialog.Root bind:open={deleteDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete Work Package</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete this work package? This action cannot be undone.
			</Dialog.Description>
		</Dialog.Header>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => (deleteDialogOpen = false)}>Cancel</Button>
			<form method="POST" action="?/delete" use:enhance>
				<input type="hidden" name="work_package_id" value={workPackageToDelete} />
				<Button type="submit" variant="destructive" onclick={confirmDelete}>Delete</Button>
			</form>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
