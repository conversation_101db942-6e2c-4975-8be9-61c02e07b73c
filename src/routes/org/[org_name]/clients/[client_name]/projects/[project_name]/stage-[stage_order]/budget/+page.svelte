<script lang="ts">
	import { SvelteSet } from 'svelte/reactivity';
	import { useSidebar } from '$lib/components/ui/sidebar';
	import BudgetSnapshotNode from '$lib/components/budget-snapshot-node.svelte';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { createSnapshotBudgetHierarchy, createCurrentBudgetHierarchy } from '$lib/budget_utils';
	import type { Tables } from '$lib/database.types';

	const { data } = $props();
	const sidebar = useSidebar();

	// Create the budget hierarchy on the client side using derived state
	const budgetHierarchy = $derived.by(() => {
		if (!data.allWbsItems || !data.rawSnapshotItems) {
			return null;
		}

		// Use the appropriate hierarchy function based on whether we're showing snapshots or current budget
		if (data.hasSnapshots) {
			return createSnapshotBudgetHierarchy(
				data.allWbsItems,
				data.rawSnapshotItems as Tables<'budget_snapshot_line_item'>[],
			);
		} else {
			return createCurrentBudgetHierarchy(
				data.allWbsItems,
				data.rawSnapshotItems as Tables<'budget_line_item_current'>[],
			);
		}
	});

	// Get the current hierarchy value
	const currentHierarchy = $derived(budgetHierarchy);

	// State for expanding/collapsing nodes
	let expandedNodeIds = new SvelteSet<string>();

	// Toggle expanded/collapsed state of a node
	function toggleNodeExpanded(nodeId: string) {
		if (expandedNodeIds.has(nodeId)) {
			expandedNodeIds.delete(nodeId);
		} else {
			expandedNodeIds.add(nodeId);
		}
	}

	let hideZeros = $state(true);
</script>

<div class="overflow-hidden p-4">
	<div class="mb-4 flex items-center justify-between">
		<div class="w-full">
			<h1 class="text-2xl font-bold">Budget - {data.stage.name}</h1>
			<div class="flex w-full items-center justify-between">
				{#if data.hasSnapshots && data.latestSnapshot}
					<div>
						<p class="text-muted-foreground">
							Snapshot from {new Date(data.latestSnapshot.freeze_date).toLocaleDateString()}
							{#if data.latestSnapshot.freeze_reason}
								- {data.latestSnapshot.freeze_reason}
							{/if}
						</p>
					</div>

					<div class="flex items-start gap-3">
						<div class="grid gap-2">
							<Label for="terms-2">Hide zero values</Label>
						</div>
						<Checkbox id="terms-2" bind:checked={hideZeros} />
					</div>
				{:else if data.showingCurrentBudget}
					<div>
						<p class="text-muted-foreground">
							Current budget (no snapshot available for this stage)
						</p>
					</div>

					<div class="flex items-start gap-3">
						<div class="grid gap-2">
							<Label for="terms-2">Hide zero values</Label>
						</div>
						<Checkbox id="terms-2" bind:checked={hideZeros} />
					</div>
				{/if}
			</div>
		</div>
	</div>

	{#if !data.hasSnapshots && !data.showingCurrentBudget}
		<div class="flex flex-col items-center justify-center p-8 text-center">
			<h2 class="mb-2 text-lg font-semibold">No Budget Data</h2>
			<p class="text-muted-foreground">No budget data is available for this project stage yet.</p>
		</div>
	{:else}
		<div
			class="-mr-4 -ml-4 overflow-x-scroll tabular-nums"
			style={`max-width: ${
				sidebar.open
					? 'calc(100vw - var(--sidebar-width) - 1rem)'
					: 'calc(100vw - var(--sidebar-width-icon) - 1rem)'
			}; max-height: calc(100vh - 14rem); position: relative;`}
		>
			<table class="budget-table relative table-fixed border-collapse">
				<thead class="sticky top-0">
					<tr class="bg-muted h-10 border-t text-sm font-medium">
						<th class="w-24 py-2 pr-2 pl-8 text-left">WBS Code</th>
						<th class="w-48 px-2 text-left">Description</th>
						<th class="w-20 px-2 text-right">Quantity</th>
						<th class="w-16 px-2 text-left">Unit</th>
						<th class="w-24 border-b px-2 text-center" colspan="3">Rate Calculation</th>
						<th class="w-24 px-2 text-right">Unit Rate</th>
						<th class="w-16 px-2 text-right">Factor</th>
						<th class="w-24 px-2 text-right">Subtotal</th>
						<th class="w-24 px-2 text-right">Cost Certainty</th>
						<th class="w-24 px-2 text-right">Design Certainty</th>
					</tr>
					<tr class="bg-muted h-10 text-sm font-medium">
						<th colspan="4"></th>
						<th class="w-24 px-2 text-right">Material Rate</th>
						<th class="w-24 px-2 text-right">Labor Rate</th>
						<th class="w-24 px-2 text-right">Productivity</th>
						<th colspan="5"></th>
					</tr>
				</thead>
				<tbody>
					{#if currentHierarchy}
						<BudgetSnapshotNode
							node={currentHierarchy}
							indent={0}
							expanded={expandedNodeIds}
							toggle={toggleNodeExpanded}
							{hideZeros}
						/>
					{/if}
				</tbody>
			</table>
		</div>
	{/if}
</div>

<style>
	.budget-table {
		min-width: 100%;
	}

	.budget-table th {
		white-space: nowrap;
	}
</style>
