import { json, error } from '@sveltejs/kit';
import { requireUser } from '$lib/server/auth';
import type { RequestHandler } from './$types';

export const DELETE: RequestHandler = async ({ locals, params }) => {
	try {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name, invoice_id } = params;

		// Verify project exists and user has access
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('project_id, client!inner(name, organization(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project:', projectError);
			throw error(404, 'Project not found');
		}

		// Verify invoice exists and belongs to the project through purchase order
		const { data: invoice, error: invoiceCheckError } = await supabase
			.from('invoice')
			.select(
				'invoice_id, purchase_order_id, purchase_order:purchase_order_id(po_number, project_id)',
			)
			.eq('invoice_id', invoice_id)
			.single();

		if (invoiceCheckError || !invoice) {
			console.error('Error fetching invoice:', invoiceCheckError);
			throw error(404, 'Invoice not found');
		}

		// Verify the invoice belongs to the correct project through purchase order
		if (invoice.purchase_order?.project_id !== projectData.project_id) {
			throw error(404, 'Invoice not found');
		}

		// Delete the invoice (this will trigger the audit function)
		const { error: deleteError } = await supabase
			.from('invoice')
			.delete()
			.eq('invoice_id', invoice_id);

		if (deleteError) {
			console.error('Error deleting invoice:', deleteError);
			throw error(500, 'Failed to delete invoice');
		}

		return json({
			success: true,
			message: `Invoice ${invoice.purchase_order?.po_number || 'Unknown PO'} deleted successfully`,
		});
	} catch (err) {
		console.error('Error in DELETE handler:', err);
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		throw error(500, 'Internal server error');
	}
};
