<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { Tables } from '$lib/database.types.js';

	const { data } = $props();
	const { project, projectStages, currentStage } = data;

	// Group stages by category
	const groupedStages = $derived.by(() => {
		if (!projectStages) return {};

		const groups: Record<string, Tables<'project_stage'>[]> = {
			'Pre-Contract': [],
			Tender: [],
			Construction: [],
			'Post-Construction': [],
		};

		// Group stages based on their name or order
		for (const stage of projectStages) {
			const stageName = stage.name.toLowerCase();

			if (
				stageName.includes('strategic') ||
				stageName.includes('preparation') ||
				stageName.includes('concept') ||
				stageName.includes('spatial')
			) {
				groups['Pre-Contract'].push(stage);
			} else if (stageName.includes('technical') || stageName.includes('tender')) {
				groups['Tender'].push(stage);
			} else if (stageName.includes('construction') || stageName.includes('manufacturing')) {
				groups['Construction'].push(stage);
			} else if (stageName.includes('handover') || stageName.includes('use')) {
				groups['Post-Construction'].push(stage);
			} else {
				// Default to pre-contract for undefined stages
				groups['Pre-Contract'].push(stage);
			}
		}

		return groups;
	});

	// Function to check if a stage is accessible
	function isStageAccessible(stage: Tables<'project_stage'>): boolean {
		// Allow access to the current stage and all completed stages
		if (stage.date_completed || (currentStage && stage.stage_order <= currentStage.stage_order)) {
			return true;
		}
		return false;
	}

	// Function to get stage status class
	function getStageStatusClass(stage: Tables<'project_stage'>): string {
		if (stage.date_completed) {
			return 'bg-green-100 border-green-500';
		} else if (currentStage && stage.stage_order === currentStage.stage_order) {
			return 'bg-blue-100 border-blue-500';
		} else if (isStageAccessible(stage)) {
			return 'bg-yellow-50 border-yellow-300';
		} else {
			return 'bg-gray-100 border-gray-300 opacity-50';
		}
	}
</script>

<div class="container mx-auto max-w-(--breakpoint-xl) py-8">
	<div class="mb-8">
		<h1 class="text-3xl font-bold">Project Stage Manager</h1>
		<!-- <p class="mt-1 text-slate-600">View and manage all project stages</p> -->
	</div>

	<div class="space-y-8">
		{#each Object.entries(groupedStages) as [category, stages] (category)}
			{#if stages.length > 0}
				<div class="rounded-lg border bg-white p-6 shadow-xs">
					<h2 class="mb-4 text-xl font-semibold">{category}</h2>

					<div class="space-y-4">
						{#each stages as stage (stage.project_stage_id)}
							<div class="relative rounded-lg border-l-4 p-4 {getStageStatusClass(stage)}">
								<div class="flex items-start justify-between">
									<div>
										<h3 class="font-medium">
											{stage.name}
											{#if stage.date_completed}
												<span class="ml-2 text-green-600">✓ Completed</span>
											{/if}
											{#if currentStage && stage.stage_order === currentStage.stage_order}
												<span class="ml-2 text-blue-600">● Current</span>
											{/if}
										</h3>

										{#if stage.description}
											<div class="prose max-w-none">
												<pre class="font-sans text-wrap">{stage.description || ''}</pre>
											</div>
										{/if}

										{#if stage.date_completed}
											<p class="mt-2 text-sm text-gray-600">
												Completed on {new Date(stage.date_completed).toLocaleDateString()}
											</p>
										{/if}
									</div>

									<div class="flex gap-4">
										{#if stage.date_completed}
											<div>
												<a
													href="/org/{encodeURIComponent(
														data.org_name,
													)}/clients/{encodeURIComponent(
														project.client.name,
													)}/projects/{encodeURIComponent(
														project.name,
													)}/stage-{stage.stage_order}/budget"
													class={isStageAccessible(stage) ? '' : 'pointer-events-none opacity-50'}
												>
													<Button variant="outline" disabled={!isStageAccessible(stage)}>
														View Stage Budget
													</Button>
												</a>
											</div>
										{/if}
										<div>
											<a
												href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
													data.client_name,
												)}/projects/{encodeURIComponent(
													data.project_name,
												)}/stage-{stage.stage_order}/gateway"
												class={isStageAccessible(stage) ? '' : 'pointer-events-none opacity-50'}
											>
												<Button variant="outline" disabled={!isStageAccessible(stage)}>
													{stage.date_completed ? 'View Gateway' : 'Go to Gateway'}
												</Button>
											</a>
										</div>
									</div>
								</div>

								<!-- Stage progress indicator -->
								{#if stage.date_completed}
									<div class="mt-3 h-2 w-full rounded-full bg-green-200">
										<div class="h-2 w-full rounded-full bg-green-500"></div>
									</div>
								{:else if currentStage && stage.stage_order === currentStage.stage_order}
									<div class="mt-3 h-2 w-full rounded-full bg-blue-100">
										<div class="h-2 w-1/12 rounded-full bg-blue-500"></div>
									</div>
								{:else}
									<div class="mt-3 h-2 w-full rounded-full bg-gray-200">
										<div class="h-2 w-0 rounded-full bg-gray-500"></div>
									</div>
								{/if}
							</div>
						{/each}
					</div>
				</div>
			{/if}
		{/each}
	</div>
</div>
