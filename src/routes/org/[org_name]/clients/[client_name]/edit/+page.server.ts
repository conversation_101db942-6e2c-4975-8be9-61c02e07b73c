import { clientSchema } from '$lib/schemas/client';
import { fail } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	await requireUser();

	const { supabase } = locals;
	const { client_name, org_name } = params;

	// Fetch the client data
	const { data: client, error } = await supabase
		.from('client')
		.select('*, organization(name)')
		.eq('organization.name', params.org_name)
		.eq('name', client_name)
		.limit(1)
		.maybeSingle();

	if (error) {
		console.error('Error fetching client:', error);
		return redirect('/', { type: 'error', message: 'Error fetching that client.' }, cookies);
	}

	if (!client) {
		return redirect('/', { type: 'error', message: 'Client not found.' }, cookies);
	}

	// Check if user has permission to manage team
	const { data: isClientAdmin } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	if (!isClientAdmin) {
		return redirect(303, `/org/${org_name}/clients/${client.name}`);
	}

	// Create a form using the client schema and pre-populate with client data
	const form = await superValidate(client, zod(clientSchema));

	return {
		form,
		client,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		// Validate form data
		const form = await superValidate(request, zod(clientSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const { supabase } = locals;
		const { client_name, org_name } = params;

		// Extract logo file and URL from form data
		// const logoFile = form.data.logo_file;
		const logoUrl = form.data.logo_url;

		// Get the client_id
		const { data: clientIdData, error: clientIdError } = await supabase
			.from('client')
			.select('client_id, organization(org_id, name)')
			.eq('organization.name', org_name)
			.eq('name', client_name)
			.limit(1)
			.maybeSingle();

		console.log({ clientIdData, clientIdError });
		if (!clientIdData?.client_id || clientIdError) {
			console.error('Error fetching client ID:', clientIdError);
			return message(form, { type: 'error', text: 'Client not found.' }, { status: 404 });
		}
		const { client_id } = clientIdData;

		// Prepare client data for update
		const clientData = { ...form.data };

		// Handle logo upload if a file was provided
		// if (logoFile && logoFile.size > 0) {
		// 	// Generate a unique filename
		// 	const fileExt = logoFile.name.split('.').pop();
		// 	const fileName = `${orgId}_${clientIdData?.client_id}_${Date.now()}.${fileExt}`;
		// 	const filePath = `${orgId}/${fileName}`;

		// 	// Upload the file to storage
		// 	const { error: uploadError } = await supabase.storage
		// 		.from('client_logos')
		// 		.upload(filePath, logoFile, {
		// 			upsert: true,
		// 			contentType: logoFile.type,
		// 		});

		// 	if (uploadError) {
		// 		console.error('Error uploading logo:', uploadError);
		// 		return message(form, { type: 'error', text: 'Error uploading logo.' }, { status: 400 });
		// 	}

		// 	// Get the public URL
		// 	const { data: publicUrlData } = supabase.storage
		// 		.from('client_logos')
		// 		.getPublicUrl(filePath);

		// 	// Set the logo URL in client data
		// 	clientData.logo_url = publicUrlData.publicUrl;
		// } else
		if (logoUrl && logoUrl.trim() !== '') {
			// Use the provided URL
			clientData.logo_url = logoUrl.trim();
		}

		// Update the client
		const { error: updateError } = await supabase
			.from('client')
			.update(clientData)
			.eq('client_id', client_id);

		if (updateError) {
			console.error('Error updating client:', updateError);
			return message(form, { type: 'error', text: 'Error updating client.' }, { status: 400 });
		}

		if (form.data.name !== client_name) {
			return redirect(
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(form.data.name)}/edit`,
				{ type: 'success', message: 'Client updated successfully.' },
				cookies,
			);
		}

		return message(form, { type: 'success', text: 'Client updated successfully.' });
	},
};
