<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { PageProps } from './$types';
	import { page } from '$app/state';

	const { data }: PageProps = $props();
	const client = $derived(data.client);
	const canManageTeam = $derived(data.canManageTeam);
</script>

<div class="container mx-auto py-8">
	<div class="mb-6 flex items-start justify-between">
		<div>
			<h1>{client.name}</h1>
			{#if client.description}
				<p class="text-muted-foreground mt-1">{client.description}</p>
			{/if}
		</div>
		<div class="flex gap-2">
			{#if canManageTeam}
				<Button
					href={`/org/${encodeURIComponent(page.params.org_name!)}/clients/${encodeURIComponent(client.name)}/invite`}
					>Manage Team</Button
				>
			{/if}
			<Button
				href={`/org/${encodeURIComponent(page.params.org_name!)}/clients/${encodeURIComponent(client.name)}/edit`}
				variant="outline">Client Settings</Button
			>
		</div>
	</div>

	<div class="mb-4 flex items-center justify-between">
		<h2 class="text-xl font-semibold">Projects</h2>
		<Button
			href={`/org/${encodeURIComponent(page.params.org_name!)}/clients/${encodeURIComponent(client.name)}/projects/new`}
			>Add Project</Button
		>
	</div>

	{#if client.project?.length === 0}
		<div class="rounded-lg border border-dashed p-8 text-center">
			<p class="text-muted-foreground">
				No projects found for this client. Add your first project to get started.
			</p>
		</div>
	{:else}
		<div class="rounded-lg border">
			<table class="w-full">
				<thead>
					<tr class="bg-muted/50 border-b">
						<th class="px-4 py-3 text-left font-medium">Name</th>
						<th class="px-4 py-3 text-left font-medium">Description</th>
						<th class="px-4 py-3 text-left font-medium">Created</th>
						<th class="px-4 py-3 text-left font-medium">Updated</th>
						<th class="px-4 py-3 text-right font-medium">Actions</th>
					</tr>
				</thead>
				<tbody>
					{#each client.project as project (project.project_id)}
						<tr class="hover:bg-muted/50 border-b last:border-b-0">
							<td class="px-4 py-3 font-medium">
								<a
									href="/org/{encodeURIComponent(
										page.params.org_name!,
									)}/clients/{encodeURIComponent(client.name)}/projects/{encodeURIComponent(
										project.name,
									)}"
									class="hover:text-primary hover:underline"
								>
									{project.name}
								</a>
							</td>
							<td class="text-muted-foreground px-4 py-3">
								{project.description || '-'}
							</td>
							<td class="text-muted-foreground px-4 py-3">
								{new Date(project.created_at).toLocaleDateString()}
							</td>
							<td class="text-muted-foreground px-4 py-3">
								{new Date(project.updated_at).toLocaleDateString()}
							</td>
							<td class="px-4 py-3 text-right">
								<Button
									href="/org/{encodeURIComponent(
										page.params.org_name!,
									)}/clients/{encodeURIComponent(client.name)}/projects/{encodeURIComponent(
										project.name,
									)}/edit"
									variant="ghost"
									size="sm"
								>
									Edit
								</Button>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	{/if}

	{#if client.client_url || client.internal_url}
		<div class="mt-6">
			<h3 class="mb-2 text-lg font-medium">Client Resources</h3>
			<div class="flex flex-wrap gap-4">
				{#if client.client_url}
					<a
						href={client.client_url}
						target="_blank"
						rel="noopener noreferrer"
						class="border-input hover:bg-accent inline-flex items-center rounded-md border px-4 py-2"
					>
						Visit Website
					</a>
				{/if}
				{#if client.internal_url}
					<a
						href={client.internal_url}
						target="_blank"
						rel="noopener noreferrer"
						class="border-input hover:bg-accent inline-flex items-center rounded-md border px-4 py-2"
					>
						{client.internal_url_description || 'Internal Site'}
					</a>
				{/if}
			</div>
		</div>
	{/if}
</div>
