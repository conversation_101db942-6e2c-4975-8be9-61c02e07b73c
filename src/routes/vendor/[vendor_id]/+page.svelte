<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { Badge } from '$lib/components/ui/badge';
	import * as Card from '$lib/components/ui/card';
	import { formatDate } from '$lib/utils';
	import PencilIcon from 'phosphor-svelte/lib/Pencil';

	const { data }: PageProps = $props();
	const { vendor, accessLevel, entityInfo } = data;

	function getAccessLevelBadgeVariant(level: string) {
		switch (level) {
			case 'organization':
				return 'default';
			case 'client':
				return 'secondary';
			case 'project':
				return 'outline';
			default:
				return 'outline';
		}
	}

	function getEntityDisplayName() {
		if (!entityInfo) return 'Unknown';

		if (accessLevel === 'organization') {
			return (entityInfo as { name: string }).name;
		} else if (accessLevel === 'client') {
			const clientInfo = entityInfo as { name: string; organization?: { name: string } };
			return `${clientInfo.organization?.name} > ${clientInfo.name}`;
		} else if (accessLevel === 'project') {
			const projectInfo = entityInfo as {
				name: string;
				client?: { name: string; organization?: { name: string } };
			};
			return `${projectInfo.client?.organization?.name} > ${projectInfo.client?.name} > ${projectInfo.name}`;
		}
		return 'Unknown';
	}

	function formatJsonField(value: unknown): string {
		if (!value) return 'Not specified';
		if (typeof value === 'object') {
			return JSON.stringify(value, null, 2);
		}
		return String(value);
	}
</script>

<div class="container mx-auto max-w-6xl py-8">
	<!-- Header -->
	<div class="mb-6 flex items-center justify-between">
		<div class="flex items-center gap-4">
			<div>
				<h1 class="text-2xl font-semibold">{vendor.name}</h1>
				<div class="text-muted-foreground flex items-center gap-2 text-sm">
					<Badge variant={getAccessLevelBadgeVariant(accessLevel)}>
						{accessLevel.charAt(0).toUpperCase() + accessLevel.slice(1)} Level
					</Badge>
					<span>•</span>
					<span>{getEntityDisplayName()}</span>
				</div>
			</div>
		</div>
		<div class="flex gap-2">
			<Button variant="outline" href={`/vendor/${vendor.vendor_id}/edit`}>
				<PencilIcon class="mr-2 size-4" />
				Edit
			</Button>
		</div>
	</div>

	<div class="grid gap-6 lg:grid-cols-3">
		<!-- Main Information -->
		<div class="space-y-6 lg:col-span-2">
			<!-- Basic Information -->
			<Card.Root>
				<Card.Header>
					<Card.Title>Basic Information</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div>
							<span class="text-muted-foreground text-sm font-medium">Vendor Name</span>
							<p class="text-sm">{vendor.name}</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Vendor Type</span>
							<p class="text-sm">{vendor.vendor_type || 'Not specified'}</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Status</span>
							<div class="flex items-center gap-2">
								<Badge variant={vendor.is_active ? 'default' : 'secondary'}>
									{vendor.is_active ? 'Active' : 'Inactive'}
								</Badge>
							</div>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Tax ID</span>
							<p class="text-sm">{vendor.tax_id || 'Not specified'}</p>
						</div>
					</div>
					{#if vendor.description}
						<div>
							<span class="text-muted-foreground text-sm font-medium">Description</span>
							<p class="text-sm">{vendor.description}</p>
						</div>
					{/if}
				</Card.Content>
			</Card.Root>

			<!-- Contact Information -->
			<Card.Root>
				<Card.Header>
					<Card.Title>Contact Information</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div>
							<span class="text-muted-foreground text-sm font-medium">Contact Name</span>
							<p class="text-sm">{vendor.contact_name || 'Not specified'}</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Contact Email</span>
							<p class="text-sm">
								{#if vendor.contact_email}
									<a href="mailto:{vendor.contact_email}" class="text-primary hover:underline">
										{vendor.contact_email}
									</a>
								{:else}
									Not specified
								{/if}
							</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Contact Phone</span>
							<p class="text-sm">
								{#if vendor.contact_phone}
									<a href="tel:{vendor.contact_phone}" class="text-primary hover:underline">
										{vendor.contact_phone}
									</a>
								{:else}
									Not specified
								{/if}
							</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Website</span>
							<p class="text-sm">
								{#if vendor.website}
									<a
										href={vendor.website}
										target="_blank"
										rel="noopener noreferrer"
										class="text-primary hover:underline"
									>
										{vendor.website}
									</a>
								{:else}
									Not specified
								{/if}
							</p>
						</div>
					</div>
					{#if vendor.contact_address}
						<div>
							<span class="text-muted-foreground text-sm font-medium">Contact Address</span>
							<p class="text-sm whitespace-pre-line">{vendor.contact_address}</p>
						</div>
					{/if}
				</Card.Content>
			</Card.Root>

			<!-- Financial Information -->
			<Card.Root>
				<Card.Header>
					<Card.Title>Financial Information</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-4">
					<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
						<div>
							<span class="text-muted-foreground text-sm font-medium">Currency</span>
							<p class="text-sm">{vendor.currency || 'USD'}</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Payment Terms</span>
							<p class="text-sm">{vendor.payment_terms || 'Not specified'}</p>
						</div>
						<div>
							<span class="text-muted-foreground text-sm font-medium">Payment Terms (Days)</span>
							<p class="text-sm">{vendor.payment_terms_days || 'Not specified'}</p>
						</div>
					</div>
					{#if vendor.credit_limit}
						<div>
							<span class="text-muted-foreground text-sm font-medium">Credit Limit</span>
							<p class="text-sm">
								{vendor.currency || 'USD'}
								{vendor.credit_limit.toLocaleString()}
							</p>
						</div>
					{/if}
				</Card.Content>
			</Card.Root>

			<!-- Additional Information -->
			{#if vendor.certification_info || vendor.insurance_info}
				<Card.Root>
					<Card.Header>
						<Card.Title>Additional Information</Card.Title>
					</Card.Header>
					<Card.Content class="space-y-4">
						{#if vendor.certification_info}
							<div>
								<span class="text-muted-foreground text-sm font-medium"
									>Certification Information</span
								>
								<pre class="bg-muted rounded p-2 text-sm text-wrap">{formatJsonField(
										vendor.certification_info,
									)}</pre>
							</div>
						{/if}
						{#if vendor.insurance_info}
							<div>
								<span class="text-muted-foreground text-sm font-medium">Insurance Information</span>
								<pre class="bg-muted rounded p-2 text-sm text-wrap">{formatJsonField(
										vendor.insurance_info,
									)}</pre>
							</div>
						{/if}
					</Card.Content>
				</Card.Root>
			{/if}
		</div>

		<!-- Sidebar -->
		<div class="space-y-6">
			<!-- Metadata -->
			<Card.Root>
				<Card.Header>
					<Card.Title>Metadata</Card.Title>
				</Card.Header>
				<Card.Content class="space-y-3">
					<div>
						<span class="text-muted-foreground text-sm font-medium">Created</span>
						<p class="text-sm">{formatDate(vendor.created_at)}</p>
					</div>
					<div>
						<span class="text-muted-foreground text-sm font-medium">Last Updated</span>
						<p class="text-sm">{formatDate(vendor.updated_at)}</p>
					</div>
				</Card.Content>
			</Card.Root>
		</div>
	</div>
</div>
