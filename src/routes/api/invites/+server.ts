import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { supabaseServiceClient } from '$lib/server/supabase_client';
import { error, json } from '@sveltejs/kit';
import crypto from 'crypto';
import { inviteRequestSchema } from '$lib/schemas/invite-request';
import { sendEmail } from '$lib/server/email';
import { PUBLIC_VERCEL_URL } from '$env/static/public';
import { dev } from '$app/environment';

export const POST: RequestHandler = async ({ request, locals }) => {
	try {
		console.log('POST /api/invites - Starting invitation process');

		const userId = locals.user?.id;
		if (!userId) {
			console.error('POST /api/invites - User not authenticated');
			throw error(401, 'Not authenticated');
		}
		console.log('POST /api/invites - User authenticated:', userId);

		let reqJson;
		try {
			reqJson = await request.json();
			console.log('POST /api/invites - Request body:', JSON.stringify(reqJson, null, 2));
		} catch (jsonError) {
			console.error('POST /api/invites - Failed to parse JSON body:', jsonError);
			console.error(
				'POST /api/invites - Content-Type header:',
				request.headers.get('content-type'),
			);
			throw error(400, 'Invalid JSON in request body');
		}

		const parsed = inviteRequestSchema.safeParse(reqJson);
		if (!parsed.success) {
			console.error('POST /api/invites - Invalid invite request:', parsed.error);
			throw error(400, parsed.error.message);
		}
		const { resourceType, resourceId, role, inviteeEmail, expiresAt } = parsed.data;
		console.log('POST /api/invites - Parsed data:', {
			resourceType,
			resourceId,
			role,
			inviteeEmail,
			expiresAt,
		});

		let joinWhat = '';
		// Validate inviter rights based on resourceType and resourceId
		console.log('POST /api/invites - Validating permissions for resourceType:', resourceType);
		switch (resourceType) {
			case 'organization': {
				console.log('POST /api/invites - Checking organization permissions');
				const { data, error: authError } = await locals.supabase.rpc(
					'current_user_has_entity_role',
					{
						entity_type_param: 'organization',
						entity_id_param: resourceId,
						min_role_param: 'admin',
					},
				);
				if (authError) {
					console.error(
						'POST /api/invites - Error validating organization inviter rights:',
						authError,
					);
					throw error(403, 'Insufficient permissions');
				}
				if (!data) {
					console.error(
						'POST /api/invites - Insufficient organization permissions for user:',
						userId,
					);
					throw error(403, 'Insufficient permissions');
				}
				console.log('POST /api/invites - Organization permissions validated');
				joinWhat = 'an organization';
				break;
			}
			case 'client': {
				console.log('POST /api/invites - Checking client permissions');
				const { data, error: authError } = await locals.supabase.rpc('get_effective_role', {
					user_id_param: userId,
					entity_type_param: 'client',
					entity_id_param: resourceId,
				});
				if (authError) {
					console.error('POST /api/invites - Error validating client inviter rights:', authError);
					throw error(403, 'Insufficient permissions');
				}
				if (!['admin', 'owner'].includes(data)) {
					console.error(
						'POST /api/invites - Insufficient client permissions for user:',
						userId,
						'role:',
						data,
					);
					throw error(403, 'Insufficient permissions');
				}
				console.log('POST /api/invites - Client permissions validated, role:', data);
				joinWhat = 'a client';
				break;
			}
			case 'project': {
				console.log('POST /api/invites - Checking project permissions');
				const { data, error: authError } = await locals.supabase.rpc('is_project_owner', {
					project_id_param: resourceId,
				});
				if (authError) {
					console.error('POST /api/invites - Error validating project inviter rights:', authError);
					throw error(403, 'Insufficient permissions');
				}
				if (!data) {
					console.error('POST /api/invites - Insufficient project permissions for user:', userId);
					throw error(403, 'Insufficient permissions');
				}
				console.log('POST /api/invites - Project permissions validated');
				joinWhat = 'a project';
				break;
			}
			default: {
				console.error('POST /api/invites - Invalid resource type:', resourceType);
				throw error(403, 'Insufficient permissions');
			}
		}

		// Generate and hash token
		console.log('POST /api/invites - Generating invitation token');
		const token = crypto.randomUUID();
		const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
		console.log('POST /api/invites - Token generated and hashed');

		console.log('POST /api/invites - Inserting invitation into database');
		const { error: insertError } = await supabaseServiceClient.from('invite').insert({
			resource_type: resourceType,
			resource_id: resourceId,
			role,
			invitee_email: inviteeEmail,
			token_hash: tokenHash,
			expires_at: expiresAt,
			inviter_id: userId,
			updated_by: userId,
		});

		if (insertError) {
			console.error('POST /api/invites - Database insert error:', insertError);
			throw error(500, insertError.message);
		}
		console.log('POST /api/invites - Invitation inserted successfully');

		// send email
		console.log('POST /api/invites - Sending invitation email to:', inviteeEmail);
		const res = await sendEmail({
			to: inviteeEmail,
			subject: `You have been invited to join ${joinWhat} on Cost Atlas`,
			html: `<p>You have been invited to join ${joinWhat}. Click <a href="${dev ? 'http' : 'https'}://${PUBLIC_VERCEL_URL}/auth/invite/${encodeURIComponent(token)}">here</a> to accept or decline the invitation.</p>`,
		});

		if (res.error) {
			console.error('POST /api/invites - Error sending email:', res.error);
			throw error(500, 'Error sending email');
		}
		console.log('POST /api/invites - Email sent successfully');

		console.log('POST /api/invites - Invitation process completed successfully');
		return json({
			success: res.success,
		});
	} catch (err) {
		console.error('POST /api/invites - Unhandled error:', err);
		// Re-throw SvelteKit errors as-is
		if (err && typeof err === 'object' && 'status' in err) {
			throw err;
		}
		// For any other errors, throw a generic 500
		throw error(500, 'Internal server error');
	}
};
