import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { signInSchema } from '$lib/schemas/auth';
import { ORG_COOKIE_NAME, ORG_COOKIE_OPTIONS } from '$lib/current-org.svelte';
import { redirect } from 'sveltekit-flash-message/server';

export const load: PageServerLoad = async ({ url }) => {
	const next = url.searchParams.get('next') ?? undefined;
	const form = await superValidate({ next }, zod(signInSchema), { errors: false });
	return { form, next };
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, url }) => {
		const form = await superValidate(request, zod(signInSchema));
		const next = form.data.next || null;
		if (!form.valid) {
			return fail(400, { form });
		}

		const {
			data: { session },
			error,
		} = await locals.supabase.auth.signInWithPassword({
			email: form.data.email,
			password: form.data.password,
		});

		if (error) {
			// Set a user-friendly error message for authentication failures
			form.errors.password = ['Invalid login credentials'];
			return fail(400, { form });
		}

		const userId = session?.user?.id;

		if (userId && !locals.orgId) {
			// Check if user has any organizations by checking memberships
			const { data: memberships } = await locals.supabase
				.from('membership')
				.select('entity_id')
				.eq('user_id', userId)
				.eq('entity_type', 'organization')
				.order('created_at', { ascending: false });

			// If user belongs to at least one organization, set the cookie to the first one
			if (memberships && memberships.length > 0) {
				const firstOrgId = memberships[0].entity_id;

				// Set the organization cookie
				cookies.set(ORG_COOKIE_NAME, firstOrgId, ORG_COOKIE_OPTIONS);
				const token = url.searchParams.get('invite');
				if (token) {
					// If the user was redirected from the invite page, redirect back to the invite page
					return redirect(
						`/auth/invite/${encodeURIComponent(token)}`,
						{
							type: 'success',
							message: 'Signed in successfully.',
							data: { newOrgId: firstOrgId },
						},
						cookies,
					);
				}
				if (next) {
					return redirect(
						next,
						{ type: 'success', message: 'Signed in successfully.', data: { newOrgId: firstOrgId } },
						cookies,
					);
				}
				// On success, redirect to home
				return redirect(
					'/',
					// { type: 'success', message: 'Signed in successfully.' },
					{ type: 'success', message: 'Signed in successfully.', data: { newOrgId: firstOrgId } },
					cookies,
				);
			}
		}

		const token = url.searchParams.get('invite');
		if (token) {
			// If the user was redirected from the invite page, redirect back to the invite page
			return redirect(303, `/auth/invite/${encodeURIComponent(token)}`);
		}
		if (next) {
			return redirect(303, next);
		}
		// On success and no organizations, redirect to new organization
		return redirect(303, '/org/new');
	},
};
