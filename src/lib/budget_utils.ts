import type { Tables } from '$lib/database.types';
import type { UpdateBudgetItem } from './schemas/project';
import { stratify, type HierarchyNode } from 'd3-hierarchy';
import { interpolateBuGn } from 'd3-scale-chromatic';
import { hsl } from 'd3-color';

// 1) Raw data shape coming from supabase.from('wbs_library_item').select(...)
export interface RawNode extends Tables<'wbs_library_item'> {
	budget_line_item_current: Tables<'budget_line_item_current'>[];
}

// Budget snapshot related interfaces
export interface BudgetSnapshotLineItem extends Tables<'budget_snapshot_line_item'> {
	wbs_library_item?: Tables<'wbs_library_item'>;
}

export interface BudgetSnapshot extends Tables<'budget_snapshot'> {
	project_stage?: {
		name: string;
		stage_order: number;
		stage: number | null;
		project_id: string;
	};
}

// 2) Node augmented with its computed costs and children
export interface NodeWithCosts extends RawNode {
	children: NodeWithCosts[];
	directCost: number;
	childrenCost: number;
	totalCost: number;
}

export interface EnhancedWbsItemTree extends Tables<'wbs_library_item'> {
	budgetItems: RawNode['budget_line_item_current'];
	directCost: number;
	childrenCost: number;
	totalCost: number;
	children: EnhancedWbsItemTree[];
	nodeId: string; // e.g. `node-${wbs_library_item_id}`
}

// Base interface for budget line items with common properties
export interface BudgetLineItemBase {
	wbs_library_item_id: string;
	quantity: number | null;
	unit_rate: number | null;
	factor: number | null;
	label?: string; // Optional label to distinguish between different budget versions (e.g., 'current', 'snapshot-stage-1')
}

// Generic interface for WBS items with budget data
export interface WbsItemWithBudgetData<T extends BudgetLineItemBase>
	extends Tables<'wbs_library_item'> {
	budgetData?: T;
	subtotal: number;
	value: number; // Used by stratify for automatic totaling
	totalFactor: number; // Cumulative factor from current item and all ancestors
}

export interface BudgetHierarchyNode<
	T extends BudgetLineItemBase = Tables<'budget_snapshot_line_item'>,
> {
	id: string;
	parentId: string | null;
	data: WbsItemWithBudgetData<T>;
	value: number;
	children?: BudgetHierarchyNode<T>[];
	depth: number;
}

export function buildBudgetTree(raw: RawNode[]): EnhancedWbsItemTree[] {
	// A) Clone each raw node into an EnhancedWbsItemTree skeleton
	const map = new Map<string, EnhancedWbsItemTree>();
	raw.forEach((r) => {
		map.set(r.wbs_library_item_id, {
			...r, // all wbs_library_item columns
			budgetItems: r.budget_line_item_current,
			directCost: 0,
			childrenCost: 0,
			totalCost: 0,
			children: [],
			nodeId: `node-${r.wbs_library_item_id}`,
		});
	});

	// B) Hook up parent ↔ child
	const roots: EnhancedWbsItemTree[] = [];
	map.forEach((node) => {
		if (node.parent_item_id && map.has(node.parent_item_id)) {
			map.get(node.parent_item_id)!.children.push(node);
		} else {
			roots.push(node);
		}
	});

	// C) Compute costs bottom-up
	function computeCosts(node: EnhancedWbsItemTree) {
		// directCost = sum(quantity * unit_rate)
		node.directCost = node.budgetItems.reduce(
			(sum, bi) => sum + bi.quantity * bi.unit_rate * (bi.factor ?? 1),
			0,
		);

		// first compute children, accumulate their totalCost
		node.children.forEach((child) => {
			computeCosts(child);
			node.childrenCost += child.totalCost;
		});

		// total = direct + children
		node.totalCost = node.directCost + node.childrenCost;
	}
	roots.forEach(computeCosts);

	return roots;
}

export function calculateUnitRate(
	item: Partial<RawNode['budget_line_item_current'][number]>,
): number {
	if (item.unit_rate_manual_override) {
		if (!item.unit_rate) {
			item.unit_rate = 0;
		}
		// If unit rate is manually overridden, return the manual value
		return item.unit_rate;
	}

	const materialCost = item.material_rate || 0;
	const laborCost = item.labor_rate || 0;
	const productivity = item.productivity_per_hour || 0;

	// If productivity is provided, calculate labor cost per unit
	const laborCostPerUnit = productivity > 0 ? laborCost / productivity : 0;

	return materialCost + laborCostPerUnit;
}

/**
 * Calculate subtotal for a budget item using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateSubtotal(
	snapshotData: Tables<'budget_snapshot_line_item'> | undefined,
): number {
	if (!snapshotData) return 0;
	return (snapshotData.quantity || 0) * (snapshotData.unit_rate || 0) * (snapshotData.factor ?? 1);
}

/**
 * Generic calculate subtotal for any budget line item type using the formula:
 * subtotal = quantity * unit_rate * (factor ?? 1)
 */
export function calculateBudgetItemSubtotal<T extends BudgetLineItemBase>(
	budgetItem: T | undefined,
): number {
	if (!budgetItem) return 0;
	return (budgetItem.quantity || 0) * (budgetItem.unit_rate || 0) * (budgetItem.factor ?? 1);
}

/**
 * Calculate total factors for all WBS items, considering hierarchical factor inheritance
 */
function calculateTotalFactors<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItemMap: Map<string, T>,
): Map<string, number> {
	const totalFactorMap = new Map<string, number>();

	// Create a map of children by parent for efficient lookup
	const childrenMap = new Map<string, Tables<'wbs_library_item'>[]>();
	wbsItems.forEach((item) => {
		const parentId = item.parent_item_id;
		if (parentId) {
			if (!childrenMap.has(parentId)) {
				childrenMap.set(parentId, []);
			}
			childrenMap.get(parentId)!.push(item);
		}
	});

	// Recursive function to calculate total factor for an item
	function calculateTotalFactor(item: Tables<'wbs_library_item'>): number {
		// Check if already calculated
		if (totalFactorMap.has(item.wbs_library_item_id)) {
			return totalFactorMap.get(item.wbs_library_item_id)!;
		}

		// Get the item's own factor from budget data
		const budgetData = budgetItemMap.get(item.wbs_library_item_id);
		const itemFactor = budgetData?.factor ?? 1;

		// Calculate parent's total factor
		let parentTotalFactor = 1;
		if (item.parent_item_id) {
			const parentItem = wbsItems.find((w) => w.wbs_library_item_id === item.parent_item_id);
			if (parentItem) {
				parentTotalFactor = calculateTotalFactor(parentItem);
			}
		}

		// Total factor is parent's total factor multiplied by this item's factor
		const totalFactor = parentTotalFactor * itemFactor;
		totalFactorMap.set(item.wbs_library_item_id, totalFactor);

		return totalFactor;
	}

	// Calculate total factors for all items
	wbsItems.forEach((item) => calculateTotalFactor(item));

	return totalFactorMap;
}

/**
 * Create hierarchical budget structure using stratify - generic version
 */
export function createGenericBudgetHierarchy<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItems: T[],
) {
	// Create a map of budget data by WBS item ID
	const budgetItemMap = new Map<string, T>();
	budgetItems.forEach((item) => {
		budgetItemMap.set(item.wbs_library_item_id, item);
	});

	// Calculate total factors for all items
	const totalFactorMap = calculateTotalFactors(wbsItems, budgetItemMap);

	// Transform WBS items to include budget data and calculated values
	const itemsWithData: WbsItemWithBudgetData<T>[] = wbsItems.map((item) => {
		const budgetData = budgetItemMap.get(item.wbs_library_item_id);
		const subtotal = calculateBudgetItemSubtotal(budgetData);
		const totalFactor = totalFactorMap.get(item.wbs_library_item_id) ?? 1;

		// For stratify value calculation:
		// - If item has budget data: use quantity * unit_rate (without factor)
		// - The totalFactor will be applied during stratify summation
		let value = 0;
		if (budgetData) {
			value = (budgetData.quantity || 0) * (budgetData.unit_rate || 0);
		}

		return {
			...item,
			budgetData,
			subtotal,
			value,
			totalFactor,
		};
	});

	// Create a project budget root node to contain all WBS items
	const projectBudgetRoot: WbsItemWithBudgetData<T> = {
		wbs_library_item_id: '__project_budget_root__',
		wbs_library_id: '__project_budget_root__',
		level: 0,
		in_level_code: '0',
		parent_item_id: null,
		code: '0',
		description: 'Project Budget Total',
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '',
		updated_at: '',
		budgetData: undefined,
		subtotal: 0,
		value: 0,
		totalFactor: 1,
	};

	// how many root items are there?
	const rootItems = itemsWithData.reduce(
		(count, item) => (!item.parent_item_id ? count + 1 : count),
		0,
	);

	// Add project budget root and update parent references for root items
	const allItems =
		rootItems > 1
			? [
					projectBudgetRoot,
					...itemsWithData.map((item) => ({
						...item,
						parent_item_id: item.parent_item_id || '__project_budget_root__',
					})),
				]
			: itemsWithData;

	// Use stratify to create hierarchical structure with proper value calculation
	const stratifyFn = stratify<WbsItemWithBudgetData<T>>()
		.id((d) => d.wbs_library_item_id)
		.parentId((d) => d.parent_item_id);

	// Create the hierarchy and apply sum calculation that accounts for totalFactor
	const root = stratifyFn(allItems).sum((d) => {
		// For items with budget data, use base value multiplied by totalFactor
		if (d.budgetData) {
			return d.value * d.totalFactor;
		}
		// For items without budget data (categories), return 0 so they don't contribute to sum
		return 0;
	});

	// Fix subtotal calculation for category items after hierarchy is built
	function fixCategorySubtotals(node: HierarchyNode<WbsItemWithBudgetData<T>>): void {
		// If this is a category item (has children but no budget data or has budget data with no quantity/rate)
		if (node.children && node.children.length > 0) {
			const hasOwnBudgetData =
				node.data.budgetData && (node.data.budgetData.quantity || node.data.budgetData.unit_rate);

			if (!hasOwnBudgetData) {
				// Calculate sum of children's subtotals
				const childrenSum = node.children.reduce(
					(sum: number, child) => sum + child.data.subtotal,
					0,
				);

				// Apply this item's own factor (if any) to the children sum
				const ownFactor = node.data.budgetData?.factor ?? 1;
				node.data.subtotal = childrenSum * ownFactor;
			}
		}

		// Recursively fix children
		if (node.children) {
			node.children.forEach(fixCategorySubtotals);
		}
	}

	// Apply the fix to the entire hierarchy
	fixCategorySubtotals(root);

	return root;
}

/**
 * Create hierarchical budget structure for current budget line items
 */
export function createCurrentBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	currentItems: Tables<'budget_line_item_current'>[],
) {
	return createGenericBudgetHierarchy(wbsItems, currentItems);
}

/**
 * Create hierarchical budget structure for budget snapshots using the generic version
 */
export function createSnapshotBudgetHierarchy(
	wbsItems: Tables<'wbs_library_item'>[],
	snapshotItems: Tables<'budget_snapshot_line_item'>[],
) {
	return createGenericBudgetHierarchy(wbsItems, snapshotItems);
}

export type SnapshotBudgetNode = ReturnType<
	typeof createGenericBudgetHierarchy<Tables<'budget_snapshot_line_item'>>
>;
export type CurrentBudgetNode = ReturnType<typeof createCurrentBudgetHierarchy>;

// Union type for budget hierarchy nodes that can handle both current and snapshot data
export type UnifiedBudgetNode = SnapshotBudgetNode | CurrentBudgetNode;

/**
 * Interface for budget data at a specific depth level
 */
export interface BudgetDataForDepth {
	wbsCode: string;
	wbsDescription: string;
	totalValue: number;
	label: string; // Used for chart x-axis labels to distinguish between budget versions
}

/**
 * Create budget data for stacked bar chart visualization at a specific WBS code depth
 *
 * @param wbsItems - Array of WBS library items
 * @param budgetItems - Array of budget line items (current or snapshot)
 * @param targetDepth - The WBS code depth level to filter for (1-based, determined by number of dots in WBS code)
 *                     e.g., depth 1 = "01", depth 2 = "01.01", depth 3 = "01.01.01"
 * @param label - Label to identify this budget version (e.g., 'current', 'snapshot-stage-1')
 * @returns Array of budget data objects for the specified depth level
 */
export function createBudgetDataForDepth<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	budgetItems: T[],
	targetDepth: number,
	label: string,
): BudgetDataForDepth[] {
	// First create the full hierarchy to get proper value calculations
	const hierarchy = createGenericBudgetHierarchy(wbsItems, budgetItems);

	// Helper function to calculate WBS depth based on code structure
	function getWbsDepth(code: string): number {
		// Count the number of dots in the WBS code to determine depth
		// e.g., "01" = depth 1, "01.01" = depth 2, "01.01.01" = depth 3
		return code.split('.').length;
	}

	// Helper function to collect all items with budget data at any depth
	function collectAllItemsWithBudget(
		node: HierarchyNode<WbsItemWithBudgetData<T>>,
		results: Array<{ code: string; description: string; value: number; depth: number }> = [],
	): Array<{ code: string; description: string; value: number; depth: number }> {
		// Skip the artificial project budget root
		if (node.data.wbs_library_item_id === '__project_budget_root__') {
			// For project budget root, just traverse children
			if (node.children) {
				node.children.forEach((child) => {
					collectAllItemsWithBudget(child, results);
				});
			}
		} else {
			const wbsDepth = getWbsDepth(node.data.code);

			// Include this item if it has budget data
			if ((node.value || 0) > 0) {
				results.push({
					code: node.data.code,
					description: node.data.description,
					value: node.value || 0,
					depth: wbsDepth,
				});
			}

			// Continue traversing children
			if (node.children) {
				node.children.forEach((child) => {
					collectAllItemsWithBudget(child, results);
				});
			}
		}

		return results;
	}

	// Collect all items with budget data
	const allItemsWithBudget = collectAllItemsWithBudget(hierarchy);

	// Find items at target depth
	const itemsAtTargetDepth = allItemsWithBudget.filter((item) => item.depth === targetDepth);

	// Start with items at target depth
	const result: BudgetDataForDepth[] = itemsAtTargetDepth.map((item) => ({
		wbsCode: item.code,
		wbsDescription: item.description,
		label: label,
		totalValue: item.value,
	}));

	// If no items at target depth, use pure fallback approach
	if (itemsAtTargetDepth.length === 0) {
		// Find the deepest available items for each branch
		const fallbackItems: BudgetDataForDepth[] = [];
		const processedBranches = new Set<string>();

		// Sort items by depth (deepest first) to find the nearest fallback
		const sortedItems = allItemsWithBudget.sort((a, b) => b.depth - a.depth);

		for (const item of sortedItems) {
			// Skip if this branch has already been processed
			if (processedBranches.has(item.code)) continue;

			// Check if this item is a child of any already processed item
			let isChildOfProcessedItem = false;
			for (const processedCode of processedBranches) {
				if (item.code.startsWith(processedCode + '.')) {
					isChildOfProcessedItem = true;
					break;
				}
			}

			// Check if this item is a parent of any already processed item
			let hasProcessedChildren = false;
			for (const processedCode of processedBranches) {
				if (processedCode.startsWith(item.code + '.')) {
					hasProcessedChildren = true;
					break;
				}
			}

			if (!isChildOfProcessedItem && !hasProcessedChildren) {
				// This item can be used as fallback
				fallbackItems.push({
					wbsCode: item.code,
					wbsDescription: item.description,
					label: label,
					totalValue: item.value,
				});

				// Mark this item as processed
				processedBranches.add(item.code);
			}
		}

		return fallbackItems;
	}

	// Find branches that need fallback (don't have target depth data)
	// Get all possible branches by looking at the WBS structure
	const allBranches = new Set<string>();

	// Helper function to get all ancestor codes for a given code
	function getAncestorCodes(code: string): string[] {
		const parts = code.split('.');
		const ancestors: string[] = [];
		for (let i = 1; i <= parts.length; i++) {
			ancestors.push(parts.slice(0, i).join('.'));
		}
		return ancestors;
	}

	// Collect all possible branch prefixes from items with budget data
	for (const item of allItemsWithBudget) {
		const ancestors = getAncestorCodes(item.code);
		ancestors.forEach((ancestor) => allBranches.add(ancestor));
	}

	// Find branches that should have target depth data but don't
	const targetDepthCodes = new Set(itemsAtTargetDepth.map((item) => item.code));
	const branchesWithTargetDepth = new Set<string>();

	// Mark branches that have target depth data
	for (const targetCode of targetDepthCodes) {
		const ancestors = getAncestorCodes(targetCode);
		// Mark all ancestors up to target depth - 1
		for (let i = 0; i < targetDepth - 1 && i < ancestors.length; i++) {
			branchesWithTargetDepth.add(ancestors[i]);
		}
	}

	// Find branches that need fallback
	const branchesNeedingFallback = new Set<string>();
	for (const branch of allBranches) {
		const branchDepth = branch.split('.').length;
		if (branchDepth === targetDepth - 1 && !branchesWithTargetDepth.has(branch)) {
			branchesNeedingFallback.add(branch);
		}
	}

	// For each branch needing fallback, find the deepest available data
	for (const branch of branchesNeedingFallback) {
		// Find all items that are ancestors of this branch or the branch itself
		const candidateItems = allItemsWithBudget.filter(
			(item) =>
				item.code === branch ||
				branch.startsWith(item.code + '.') ||
				item.code.startsWith(branch + '.'),
		);

		// Find the deepest item that's not deeper than target depth
		let bestFallback: (typeof candidateItems)[0] | null = null;
		for (const candidate of candidateItems) {
			if (
				candidate.depth < targetDepth &&
				(bestFallback === null || candidate.depth > bestFallback.depth)
			) {
				bestFallback = candidate;
			}
		}

		if (bestFallback) {
			result.push({
				wbsCode: bestFallback.code,
				wbsDescription: bestFallback.description,
				label: label,
				totalValue: bestFallback.value,
			});
		}
	}

	return result;
}

/**
 * Interface for stacked bar chart data
 */
export interface StackedBudgetData {
	snapshot: string;
	[wbsCode: string]: string | number;
}

/**
 * Create budget data for stacked bar chart visualization from multiple snapshots
 *
 * @param wbsItems - Array of WBS library items
 * @param snapshotsData - Array of objects containing snapshot info and budget items
 * @param targetDepth - The WBS code depth level to filter for (1-based)
 * @returns Array of objects suitable for stacked bar chart, where each object represents one snapshot
 */
export function createStackedBudgetData<T extends BudgetLineItemBase>(
	wbsItems: Tables<'wbs_library_item'>[],
	snapshotsData: Array<{
		snapshot:
			| BudgetSnapshot
			| {
					budget_snapshot_id: string;
					project_stage_id: string;
					freeze_date: string;
					freeze_reason: string | null;
					created_by_user_id: string;
					created_at: string;
					updated_at: string;
					project_stage?: {
						name: string;
						stage_order: number;
						stage: number | null;
						project_id: string;
					};
			  };
		budgetItems: T[];
	}>,
	targetDepth: number,
): StackedBudgetData[] {
	// Get all unique WBS codes at the target depth across all snapshots
	const allWbsCodes = new Set<string>();

	// First pass: collect all WBS codes at target depth from all snapshots
	snapshotsData.forEach(({ budgetItems }) => {
		const depthData = createBudgetDataForDepth(wbsItems, budgetItems, targetDepth, 'temp');
		depthData.forEach((item) => {
			// Include all WBS codes returned by fallback mechanism, even if they have zero value in this snapshot
			// This ensures consistent coverage across all snapshots
			allWbsCodes.add(item.wbsCode);
		});
	});

	// Convert to sorted array for consistent ordering
	const sortedWbsCodes = Array.from(allWbsCodes).sort((a, b) =>
		a.localeCompare(b, undefined, { numeric: true }),
	);

	// Second pass: create stacked data for each snapshot
	const stackedData: StackedBudgetData[] = snapshotsData.map(({ snapshot, budgetItems }) => {
		const depthData = createBudgetDataForDepth(wbsItems, budgetItems, targetDepth, 'temp');

		// Create a map for quick lookup
		const valueMap = new Map<string, number>();
		depthData.forEach((item) => {
			valueMap.set(item.wbsCode, item.totalValue);
		});

		// Create the stacked data object
		const stackedItem: StackedBudgetData = {
			snapshot:
				snapshot.project_stage?.name || `Stage ${snapshot.project_stage?.stage_order || 'Unknown'}`,
		};

		// Add values for each WBS code (0 if not present in this snapshot)
		sortedWbsCodes.forEach((wbsCode) => {
			stackedItem[wbsCode] = Math.round(valueMap.get(wbsCode) || 0);
		});

		return stackedItem;
	});

	return stackedData;
}

/**
 * Generate hierarchical colors for WBS codes based on their structure and depth
 * Colors are assigned deterministically based on WBS code structure to ensure consistency across depth levels
 *
 * @param wbsCodes - Array of WBS codes to generate colors for
 * @returns Map of WBS codes to their assigned colors
 */
export function generateWbsColors(wbsCodes: string[]): Map<string, string> {
	const colorMap = new Map<string, string>();

	// Helper function to get WBS depth based on code structure
	function getWbsDepth(code: string): number {
		return code.split('.').length;
	}

	// Helper function to get parent WBS code
	function getParentCode(code: string): string | null {
		const parts = code.split('.');
		if (parts.length <= 1) return null;
		return parts.slice(0, -1).join('.');
	}

	// Helper function to get level 2 code from any WBS code
	function getLevel2Code(code: string): string | null {
		const parts = code.split('.');
		if (parts.length < 2) return null;
		return parts.slice(0, 2).join('.');
	}

	// Generate all parent codes that we need
	const allCodes = new Set<string>();
	wbsCodes.forEach((code) => {
		allCodes.add(code);
		let parentCode = getParentCode(code);
		while (parentCode) {
			allCodes.add(parentCode);
			parentCode = getParentCode(parentCode);
		}
	});

	// Group codes by depth level
	const codesByDepth = new Map<number, string[]>();
	Array.from(allCodes).forEach((code) => {
		const depth = getWbsDepth(code);
		if (!codesByDepth.has(depth)) {
			codesByDepth.set(depth, []);
		}
		codesByDepth.get(depth)!.push(code);
	});

	// Sort codes within each depth level for consistent ordering
	codesByDepth.forEach((codes) => {
		codes.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));
	});

	// Get all level 2 codes for consistent base color assignment
	const level2Codes = codesByDepth.get(2) || [];

	// Process each depth level
	const sortedDepths = Array.from(codesByDepth.keys()).sort((a, b) => a - b);

	sortedDepths.forEach((depth) => {
		const codesAtDepth = codesByDepth.get(depth)!;

		if (depth === 1) {
			// Level 1: Use a consistent base color
			codesAtDepth.forEach((code) => {
				colorMap.set(code, interpolateBuGn(0.5));
			});
		} else if (depth === 2) {
			// Level 2: Create distinct color families using a more limited range
			// Use only the middle portion of the BuGn gradient to avoid extreme light/dark colors
			codesAtDepth.forEach((code) => {
				const globalIndex = level2Codes.indexOf(code);
				// Use a more limited range (0.2 to 0.8) to create more distinct but not extreme colors
				const colorValue =
					level2Codes.length > 1 ? 0.2 + (0.6 * globalIndex) / (level2Codes.length - 1) : 0.5;
				colorMap.set(code, interpolateBuGn(colorValue));
			});
		} else if (depth === 3) {
			// Level 3: Create distinct variations within each level 2 parent's color family
			codesAtDepth.forEach((code) => {
				const parentCode = getParentCode(code);
				if (parentCode && colorMap.has(parentCode)) {
					const parentColor = colorMap.get(parentCode)!;

					// Find ALL siblings at this level with the same parent (not just currently visible ones)
					const allSiblingsAtDepth = Array.from(allCodes).filter(
						(c) => getWbsDepth(c) === depth && getParentCode(c) === parentCode,
					);
					allSiblingsAtDepth.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));

					const siblingIndex = allSiblingsAtDepth.indexOf(code);
					const siblingCount = allSiblingsAtDepth.length;

					// Create noticeable but harmonious variations within the parent's color family
					let modifiedColor: string;

					if (parentColor.startsWith('hsl(var(--primary))')) {
						// For primary color, create variations using opacity
						const opacity =
							siblingCount > 1 ? 0.5 + (0.4 * siblingIndex) / (siblingCount - 1) : 0.7;
						modifiedColor = `hsla(var(--primary), ${opacity})`;
					} else {
						// For other colors, parse and make noticeable modifications within the color family
						try {
							const parsedColor = hsl(parentColor);
							if (parsedColor) {
								// Make noticeable lightness adjustments (stay within ±0.2 of parent)
								const lightnessAdjustment =
									siblingCount > 1 ? -0.2 + (0.4 * siblingIndex) / (siblingCount - 1) : 0;
								parsedColor.l = Math.max(0.15, Math.min(0.85, parsedColor.l + lightnessAdjustment));

								// Also adjust saturation for more variation
								const saturationAdjustment =
									siblingCount > 1 ? -0.15 + (0.3 * siblingIndex) / (siblingCount - 1) : 0;
								parsedColor.s = Math.max(0.2, Math.min(1, parsedColor.s + saturationAdjustment));

								modifiedColor = parsedColor.toString();
							} else {
								// Fallback to parent color with opacity variation
								const opacity =
									siblingCount > 1 ? 0.5 + (0.4 * siblingIndex) / (siblingCount - 1) : 0.7;
								modifiedColor = parentColor.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
							}
						} catch {
							// Fallback to parent color with opacity variation
							const opacity =
								siblingCount > 1 ? 0.5 + (0.4 * siblingIndex) / (siblingCount - 1) : 0.7;
							modifiedColor = parentColor.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
						}
					}

					colorMap.set(code, modifiedColor);
				} else {
					// Fallback: use deterministic color based on level 2 parent
					const level2Parent = getLevel2Code(code);
					if (level2Parent && level2Codes.includes(level2Parent)) {
						const level2Index = level2Codes.indexOf(level2Parent);
						const baseColorValue =
							level2Codes.length > 1 ? 0.2 + (0.6 * level2Index) / (level2Codes.length - 1) : 0.5;
						colorMap.set(code, interpolateBuGn(baseColorValue));
					} else {
						// Final fallback - use a neutral color
						colorMap.set(code, interpolateBuGn(0.5));
					}
				}
			});
		} else {
			// Level 4+: Use nearly identical colors to parent (almost no variation)
			codesAtDepth.forEach((code) => {
				const parentCode = getParentCode(code);
				if (parentCode && colorMap.has(parentCode)) {
					const parentColor = colorMap.get(parentCode)!;

					// Find ALL siblings at this level with the same parent (not just currently visible ones)
					const allSiblingsAtDepth = Array.from(allCodes).filter(
						(c) => getWbsDepth(c) === depth && getParentCode(c) === parentCode,
					);
					allSiblingsAtDepth.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));

					const siblingIndex = allSiblingsAtDepth.indexOf(code);
					const siblingCount = allSiblingsAtDepth.length;

					// Create extremely subtle variations of the parent color (barely perceptible)
					let modifiedColor: string;

					if (parentColor.startsWith('hsl(var(--primary))')) {
						// For primary color, create minimal variations using opacity
						const opacity =
							siblingCount > 1 ? 0.92 + (0.06 * siblingIndex) / (siblingCount - 1) : 0.95;
						modifiedColor = `hsla(var(--primary), ${opacity})`;
					} else {
						// For other colors, parse and make extremely minimal modifications
						try {
							const parsedColor = hsl(parentColor);
							if (parsedColor) {
								// Make extremely subtle lightness adjustments (stay within ±0.02 of parent)
								const lightnessAdjustment =
									siblingCount > 1 ? -0.02 + (0.04 * siblingIndex) / (siblingCount - 1) : 0;
								parsedColor.l = Math.max(0.1, Math.min(0.9, parsedColor.l + lightnessAdjustment));

								// Extremely minimal saturation adjustments
								const saturationAdjustment =
									siblingCount > 1 ? -0.01 + (0.02 * siblingIndex) / (siblingCount - 1) : 0;
								parsedColor.s = Math.max(0.1, Math.min(1, parsedColor.s + saturationAdjustment));

								modifiedColor = parsedColor.toString();
							} else {
								// Fallback to parent color with minimal opacity variation
								const opacity =
									siblingCount > 1 ? 0.92 + (0.06 * siblingIndex) / (siblingCount - 1) : 0.95;
								modifiedColor = parentColor.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
							}
						} catch {
							// Fallback to parent color with minimal opacity variation
							const opacity =
								siblingCount > 1 ? 0.92 + (0.06 * siblingIndex) / (siblingCount - 1) : 0.95;
							modifiedColor = parentColor.replace('rgb(', 'rgba(').replace(')', `, ${opacity})`);
						}
					}

					colorMap.set(code, modifiedColor);
				} else {
					// Fallback: use deterministic color based on level 2 parent
					const level2Parent = getLevel2Code(code);
					if (level2Parent && level2Codes.includes(level2Parent)) {
						const level2Index = level2Codes.indexOf(level2Parent);
						const baseColorValue =
							level2Codes.length > 1 ? 0.2 + (0.6 * level2Index) / (level2Codes.length - 1) : 0.5;
						colorMap.set(code, interpolateBuGn(baseColorValue));
					} else {
						// Final fallback - use a neutral color
						colorMap.set(code, interpolateBuGn(0.5));
					}
				}
			});
		}
	});

	return colorMap;
}

/**
 * Create chart configuration for stacked budget chart
 *
 * @param wbsCodes - Array of WBS codes that will be used as stack components
 * @param wbsItems - Array of WBS library items for descriptions
 * @returns ChartConfig object for use with the chart component
 */
export function createStackedBudgetChartConfig(
	wbsCodes: string[],
	wbsItems: Tables<'wbs_library_item'>[],
): Record<string, { label: string; color: string }> {
	// Create a map for quick WBS item lookup
	const wbsMap = new Map<string, Tables<'wbs_library_item'>>();
	wbsItems.forEach((item) => {
		wbsMap.set(item.code, item);
	});

	// Generate hierarchical colors based on WBS structure
	const colorMap = generateWbsColors(wbsCodes);

	const config: Record<string, { label: string; color: string }> = {};

	wbsCodes.forEach((code) => {
		const wbsItem = wbsMap.get(code);
		const description = wbsItem?.description || code;

		// Create a shorter label for the legend
		const label =
			description.length > 20
				? `${code}: ${description.slice(0, 15)}...`
				: `${code}: ${description}`;

		config[code] = {
			label,
			color: colorMap.get(code) || interpolateBuGn(0), // Fallback to interpolateCool if color not found
		};
	});

	return config;
}

// Enhanced BudgetLineItem with UI state
export interface BudgetLineItem extends Partial<UpdateBudgetItem> {
	budget_line_item_id?: string;
	project_id: string;
	wbs_library_item_id: string;
	quantity: number;
	unit: string | null;
	material_rate: number;
	labor_rate: number | null;
	productivity_per_hour: number | null;
	unit_rate_manual_override: boolean;
	unit_rate: number;
	remarks: string | null;
	cost_certainty: number | null;
	design_certainty: number | null;
	created_at?: string;
	updated_at?: string;
	// UI-specific fields
	wbs_code?: string;
	description?: string;
	subtotal?: number;
	extension?: number;
	isParent?: boolean;
	level?: number;
}
