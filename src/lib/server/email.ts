import { dev } from '$app/environment';
import { RESEND_API_KEY } from '$env/static/private';
import { PUBLIC_FROM_EMAIL_ADDRESS } from '$env/static/public';
import { Resend } from 'resend';

const resend = new Resend(RESEND_API_KEY ?? '');

const FROM = PUBLIC_FROM_EMAIL_ADDRESS;
const REPLY_TO = FROM;

// TODO: add this to a queue so that we can send emails in the background
// and not block the request
export const sendEmail = async ({
	to,
	subject,
	text,
	html,
}: {
	to: string;
	subject: string;
	text?: string;
	html?: string;
}) => {
	console.log('sendEmail - Starting email send process');
	console.log('sendEmail - Parameters:', { to, subject, hasText: !!text, hasHtml: !!html });
	console.log('sendEmail - Environment:', { dev, FROM, REPLY_TO });
	console.log('sendEmail - RESEND_API_KEY exists:', !!RESEND_API_KEY);

	if (!to) {
		console.error('sendEmail - No recipient provided');
		throw new Error('No recipient provided');
	}

	if (dev) {
		console.log('sendEmail - Email not sent in dev mode. To:', { to, subject, text, html });
		return { success: true };
	}

	// Send an email
	// https://resend.com/docs/send-email
	if (html) {
		console.log('sendEmail - Sending HTML email via Resend');
		try {
			const { data, error } = await resend.emails.send({
				from: FROM,
				to,
				replyTo: REPLY_TO,
				subject,
				html,
			});
			if (error) {
				console.error('sendEmail - Error sending HTML email:', error);
				return { error };
			}
			console.log('sendEmail - HTML email sent successfully', { data });
			return { success: true };
		} catch (err) {
			console.error('sendEmail - Exception sending HTML email:', err);
			return { error: err };
		}
	}
	if (text) {
		console.log('sendEmail - Sending text email via Resend');
		try {
			const { data, error } = await resend.emails.send({
				from: FROM,
				to,
				replyTo: REPLY_TO,
				subject,
				text,
			});
			if (error) {
				console.error('sendEmail - Error sending text email:', error);
				return { error };
			}
			console.log('sendEmail - Text email sent successfully', { data });
			return { success: true };
		} catch (err) {
			console.error('sendEmail - Exception sending text email:', err);
			return { error: err };
		}
	}
	console.error('sendEmail - Neither text nor html provided');
	return { error: new Error('Either text or html must be provided') };
};
