import { fail } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { invoiceSchema, invoiceModalSchema } from '$lib/schemas/invoice';
import { requireUser } from '$lib/server/auth';
import type { RequestEvent } from '@sveltejs/kit';

export const createInvoice = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(invoiceSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the invoice data
	const invoiceData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the invoice
	const { data: invoice, error: invoiceError } = await supabase
		.from('invoice')
		.insert(invoiceData)
		.select(
			`
			invoice_id,
			purchase_order_id,
			description,
			invoice_date,
			account,
			amount,
			period,
			post_date,
			notes,
			created_at,
			purchase_order:purchase_order_id(po_number, vendor:vendor_id(name))
		`,
		)
		.single();

	if (invoiceError) {
		console.error('Error creating invoice:', invoiceError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create invoice' },
		});
	}

	return message(form, {
		type: 'success',
		text: `Invoice ${invoice.purchase_order?.po_number} created successfully`,
	});
};

export const createInvoiceModal = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(invoiceModalSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the invoice data
	const invoiceData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the invoice
	const { data: invoice, error: invoiceError } = await supabase
		.from('invoice')
		.insert(invoiceData)
		.select(
			`
			invoice_id,
			purchase_order_id,
			description,
			invoice_date,
			account,
			amount,
			period,
			post_date,
			notes,
			created_at,
			purchase_order:purchase_order_id(po_number, vendor:vendor_id(name))
		`,
		)
		.single();

	if (invoiceError) {
		console.error('Error creating invoice:', invoiceError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create invoice' },
		});
	}

	// Return the invoice data for modal handling
	return {
		form,
		invoice: {
			invoice_id: invoice.invoice_id,
			purchase_order_id: invoice.purchase_order_id,
			po_number: invoice.purchase_order?.po_number || 'Unknown PO',
			description: invoice.description,
			invoice_date: invoice.invoice_date,
			vendor_name: invoice.purchase_order?.vendor?.name || 'Unknown Vendor',
			account: invoice.account,
			amount: invoice.amount,
			period: invoice.period,
			post_date: invoice.post_date,
			notes: invoice.notes,
			created_at: invoice.created_at,
			created_by_name: user.email || 'Unknown User',
		},
		message: {
			type: 'success',
			text: `Invoice ${invoice.purchase_order?.po_number} created successfully`,
		},
	};
};
