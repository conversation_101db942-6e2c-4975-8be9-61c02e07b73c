<script lang="ts">
	import * as Chart from '$lib/components/ui/chart/index.js';
	import { scaleBand } from 'd3-scale';
	import { BarChart, type ChartContextValue } from 'layerchart';
	import { cubicInOut } from 'svelte/easing';
	import Button from '$lib/components/ui/button/button.svelte';

	interface StackedBarData {
		[key: string]: string | number;
	}

	interface StackedBarProps {
		data: StackedBarData[];
		chartConfig: Chart.ChartConfig;
		xKey: string;
		title?: string;
		description?: string;
		height?: number;
	}

	let {
		data,
		chartConfig,
		xKey,
		title = 'Stacked Bar Chart',
		description = '',
		height = 400,
	}: StackedBarProps = $props();

	let context = $state<ChartContextValue>();

	// Extract series keys from chartConfig (excluding the x-axis key)
	const seriesKeys = $derived(Object.keys(chartConfig).filter((key) => key !== xKey));
	let hoveredKey = $state<string | null>(null);
	let selectedKey = $state<string | null>(null);

	// Create series configuration for LayerChart
	const series = $derived(
		selectedKey
			? seriesKeys
					.filter((key) => selectedKey && key.startsWith(selectedKey))
					.map((key, index) => {
						return {
							key,
							label: chartConfig[key]?.label || key,
							color: chartConfig[key]?.color || '#000000',
							props: index === 0 ? { rounded: 'bottom' as const } : undefined,
						};
					})
			: hoveredKey
				? seriesKeys.map((key, index) => {
						return {
							key,
							label: chartConfig[key]?.label || key,
							color:
								hoveredKey && key.startsWith(hoveredKey)
									? chartConfig[key]?.color
									: 'var(--color-muted)', // Simple fallback color
							props: index === 0 ? { rounded: 'bottom' as const } : undefined,
						};
					})
				: seriesKeys.map((key, index) => {
						return {
							key,
							label: chartConfig[key]?.label || key,
							color: chartConfig[key]?.color || '#000000', // Simple fallback color
							props: index === 0 ? { rounded: 'bottom' as const } : undefined,
						};
					}),
	);
</script>

<article>
	<header>
		<h2>{title}</h2>
		{#if description}
			<p>{description}</p>
		{/if}
	</header>
	<div>
		<div class="flex gap-6">
			<Chart.Container config={chartConfig} class="-ml-16 aspect-auto flex-1 pl-24">
				<div class="chart-stacked-bar" style="height: {height}px; width: 100%;">
					<BarChart
						bind:context
						{data}
						xScale={scaleBand().padding(0.35)}
						x={xKey}
						axis={true}
						rule={false}
						{series}
						seriesLayout="stack"
						props={{
							bars: {
								stroke: 'none',
								initialY: context?.height,
								initialHeight: 0,
								motion: {
									y: { type: 'tween', duration: 500, easing: cubicInOut },
									height: { type: 'tween', duration: 500, easing: cubicInOut },
								},
							},
							xAxis: {
								tickLabelProps: { lineHeight: '1.05em' },
								format: (d: string | number): string => {
									if (typeof d === 'string' && d.length > 12) {
										// Split long labels into multiple lines
										const words = d.split(' ');
										const lines = [];
										let currentLine = '';

										for (const word of words) {
											if ((currentLine + word).length > 12 && currentLine.length > 0) {
												lines.push(currentLine.trim());
												currentLine = word + ' ';
											} else {
												currentLine += word + ' ';
											}
										}
										if (currentLine.trim()) {
											lines.push(currentLine.trim());
										}

										return lines.join('\n');
									}
									return String(d);
								},
							},
							yAxis: {
								format: (d: string | number): string => {
									// Format budget values with appropriate currency/number formatting
									if (typeof d === 'number') {
										return new Intl.NumberFormat(undefined, {
											style: 'currency',
											currency: 'SEK',
											minimumFractionDigits: 0,
											maximumFractionDigits: 2,
											notation: 'compact',
										}).format(d);
									}
									return String(d);
								},
							},
						}}
						legend={false}
					>
						{#snippet tooltip()}
							<Chart.Tooltip />
						{/snippet}
					</BarChart>
				</div>
			</Chart.Container>

			<!-- Custom Legend -->
			<div
				class="flex min-w-48 flex-col items-start gap-0 overflow-y-auto"
				style="height: {height}px;"
			>
				{#each seriesKeys as key (key)}
					<Button
						size="sm"
						variant="ghost"
						class={[
							'flex w-full items-center justify-start gap-2 text-xs',
							selectedKey === key && 'bg-primary-foreground text-white',
							selectedKey !== key && 'text-muted-foreground',
						]}
						onmouseenter={() => (hoveredKey = key)}
						onmouseleave={() => (hoveredKey = null)}
						onclick={() => (selectedKey === key ? (selectedKey = null) : (selectedKey = key))}
					>
						<div
							class="size-3 rounded-sm"
							style="background-color: {chartConfig[key]?.color || '#000000'}"
						></div>
						<span class="leading-none">{chartConfig[key]?.label || key}</span>
					</Button>
				{/each}
			</div>
		</div>
	</div>
</article>
