/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createMockSupabaseClient } from '$tests/mocks/supabase';

// Mock SvelteKit fail before importing it
vi.mock('@sveltejs/kit', async () => {
	const original = await vi.importActual('@sveltejs/kit');
	return {
		...original,
		fail: vi.fn().mockImplementation((code, data) => ({ status: code, ...data })),
	};
});

// Mock the superforms modules
vi.mock('sveltekit-superforms/server', async () => {
	return {
		superValidate: vi.fn().mockImplementation(async (request, validator) => {
			// If request is an object with data property, treat it as pre-validated form
			if (request && typeof request === 'object' && 'data' in request) {
				return request;
			}

			// Otherwise assume it's a standard request
			const formData = await request?.formData?.();
			const data = Object.fromEntries(formData?.entries() || []);
			const validation = validator.safeParse(data);

			return {
				valid: validation.success,
				data: validation.success ? validation.data : data,
				errors: validation.success ? {} : validation.error.flatten().fieldErrors,
			};
		}),
		message: vi.fn().mockImplementation((form, message) => {
			return { form, flashMessage: message };
		}),
	};
});

// Mock the superforms adapter
vi.mock('sveltekit-superforms/adapters', () => {
	return {
		zod: vi.fn().mockImplementation((schema) => schema),
		zod4: vi.fn().mockImplementation((schema) => schema),
	};
});

// Now import the mocked functions after all mocks are set up
import { fail as _fail } from '@sveltejs/kit';
import { message as _message, superValidate } from 'sveltekit-superforms/server';
import { zod as _zod } from 'sveltekit-superforms/adapters';
import { signUpSchema as _signUpSchema } from '$lib/schemas/auth';

// Import the actions from the signup page
import { actions } from '../../../routes/auth/signup/+page.server';

describe('Signup flow integration', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should successfully create a new user with valid data', async () => {
		// Mock form data
		const formData = new FormData();
		formData.append('email', '<EMAIL>');
		formData.append('password', 'password123');

		// Mock request
		const request = {
			formData: () => Promise.resolve(formData),
		};

		// Mock locals with custom supabase response
		const mockSupabase = createMockSupabaseClient({
			signUp: { data: { user: { id: 'new-user-id' } }, error: null },
		});

		const locals = {
			supabase: mockSupabase,
		};

		// Run the signup action
		const result = await actions.default({ request, locals } as Parameters<
			(typeof actions)['default']
		>[0]);

		// Verify the result
		expect(result).toHaveProperty('flashMessage');
		expect(result.flashMessage).toEqual({
			type: 'success',
			text: 'Account created successfully. Please check your email to verify.',
		});

		// Verify Supabase was called with correct parameters
		expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
			email: '<EMAIL>',
			password: 'password123',
			options: {
				emailRedirectTo: 'http://localhost:5173/auth/signin',
			},
		});
	});

	it('should return validation errors with invalid data', async () => {
		// Mock form data with invalid email
		const formData = new FormData();
		formData.append('email', 'invalid-email');
		formData.append('password', 'password123');

		// Mock request
		const request = {
			formData: () => Promise.resolve(formData),
		};

		// Setup mock validation result
		vi.mocked(superValidate).mockResolvedValueOnce({
			valid: false,
			data: { email: 'invalid-email', password: 'password123' },
			errors: { email: ['Invalid email address'] },
		});

		// Mock locals
		const mockSupabase = createMockSupabaseClient();
		const locals = { supabase: mockSupabase };

		// mockFail is already defined at the top of the file
		// and properly mocked via vi.mock

		// Run the signup action
		const result = await actions.default({ request, locals } as Parameters<
			(typeof actions)['default']
		>[0]);

		// Verify fail was called with correct parameters
		expect(result).toBeDefined();

		// Verify Supabase signUp was not called
		expect(mockSupabase.auth.signUp).not.toHaveBeenCalled();
	});

	it('should handle Supabase auth errors', async () => {
		// Mock form data
		const formData = new FormData();
		formData.append('email', '<EMAIL>');
		formData.append('password', 'password123');

		// Mock request
		const request = {
			formData: () => Promise.resolve(formData),
		};

		// Setup mock validation result
		vi.mocked(superValidate).mockResolvedValueOnce({
			valid: true,
			data: { email: '<EMAIL>', password: 'password123' },
		});

		// Mock locals with auth error
		const mockSupabase = createMockSupabaseClient({
			signUp: { data: {}, error: { message: 'User already exists' } },
		});

		const locals = { supabase: mockSupabase };

		// Run the signup action
		const result = await actions.default({ request, locals } as Parameters<
			(typeof actions)['default']
		>[0]);

		// Verify error message was returned
		expect(result).toHaveProperty('flashMessage');
		expect(result.flashMessage).toEqual({
			type: 'error',
			text: 'User already exists',
		});
	});
});
