import { describe, it, expect } from 'vitest';
import {
	createSnapshotBudgetHierarchy,
	calculateSubtotal,
	createCurrentBudgetHierarchy,
	createBudgetDataForDepth,
} from '$lib/budget_utils';
import type { Tables } from '$lib/database.types';

// Helper function to create a WBS item
function createWbsItem(
	id: string,
	parentId: string | null,
	code: string,
	description: string,
	level: number,
): Tables<'wbs_library_item'> {
	return {
		wbs_library_item_id: id,
		wbs_library_id: '550e8400-e29b-41d4-a716-446655440001',
		level,
		in_level_code: code.split('.').pop() || code,
		parent_item_id: parentId,
		code,
		description,
		cost_scope: null,
		item_type: 'Standard',
		client_id: null,
		project_id: null,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
	};
}

// Helper function to create a budget snapshot line item
function createSnapshotItem(
	id: string,
	wbsItemId: string,
	quantity: number,
	unitRate: number,
	factor?: number,
): Tables<'budget_snapshot_line_item'> {
	return {
		budget_snapshot_line_item_id: id,
		budget_snapshot_id: '550e8400-e29b-41d4-a716-446655440002',
		wbs_library_item_id: wbsItemId,
		quantity,
		unit: 'each',
		material_rate: unitRate,
		labor_rate: null,
		productivity_per_hour: null,
		unit_rate_manual_override: false,
		unit_rate: unitRate,
		factor: factor ?? 1,
		remarks: null,
		cost_certainty: null,
		design_certainty: null,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
	};
}

// Helper function to create a current budget item
function createCurrentBudgetItem(
	projectId: string,
	wbsItemId: string,
	quantity: number,
	unitRate: number,
	factor?: number,
): Tables<'budget_line_item_current'> {
	return {
		budget_line_item_id: `550e8400-e29b-41d4-a716-${Math.floor(Math.random() * 1000000)
			.toString()
			.padStart(12, '0')}`,
		project_id: projectId,
		wbs_library_item_id: wbsItemId,
		quantity,
		unit: 'each',
		material_rate: 0,
		labor_rate: null,
		productivity_per_hour: null,
		unit_rate_manual_override: false,
		unit_rate: unitRate,
		factor: factor ?? 1,
		remarks: null,
		cost_certainty: null,
		design_certainty: null,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z',
	};
}

describe('Budget Hierarchy', () => {
	describe('calculateSubtotal', () => {
		it('calculates subtotal correctly with factor', () => {
			const snapshotData = createSnapshotItem(
				'550e8400-e29b-41d4-a716-446655440101',
				'item1',
				10,
				100,
				1.5,
			);
			const result = calculateSubtotal(snapshotData);
			expect(result).toBe(1500); // 10 * 100 * 1.5
		});

		it('calculates subtotal correctly without factor', () => {
			const snapshotData = createSnapshotItem(
				'550e8400-e29b-41d4-a716-446655440102',
				'item1',
				5,
				200,
			);
			const result = calculateSubtotal(snapshotData);
			expect(result).toBe(1000); // 5 * 200 * 1
		});

		it('returns 0 for undefined snapshot data', () => {
			const result = calculateSubtotal(undefined);
			expect(result).toBe(0);
		});
	});

	describe('createSnapshotBudgetHierarchy', () => {
		it('creates a simple hierarchy with parent and child', () => {
			const wbsItems = [
				createWbsItem('parent-1', null, '1', 'Parent Item', 1),
				createWbsItem('child-1', 'parent-1', '1.1', 'Child Item', 2),
			];

			const snapshotItems = [
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440201', 'child-1', 2, 50), // subtotal: 100
			];

			const hierarchy = createSnapshotBudgetHierarchy(wbsItems, snapshotItems);

			// With single root WBS item, should return that item directly (not project budget root)
			expect(hierarchy.id).toBe('parent-1');
			expect(hierarchy.data.code).toBe('1');
			expect(hierarchy.data.description).toBe('Parent Item');
			expect(hierarchy.value).toBe(100); // Should roll up all child values
			expect(hierarchy.children).toHaveLength(1);

			const child = hierarchy.children![0];
			expect(child.id).toBe('child-1');
			expect(child.data.code).toBe('1.1');
			expect(child.value).toBe(100); // 2 * 50 * 1
			expect(child.data.subtotal).toBe(100);
		});

		it('creates hierarchy with multiple levels', () => {
			const wbsItems = [
				createWbsItem('root', null, '1', 'Root', 1),
				createWbsItem('level2-1', 'root', '1.1', 'Level 2 Item 1', 2),
				createWbsItem('level2-2', 'root', '1.2', 'Level 2 Item 2', 2),
				createWbsItem('level3-1', 'level2-1', '1.1.1', 'Level 3 Item 1', 3),
				createWbsItem('level3-2', 'level2-1', '1.1.2', 'Level 3 Item 2', 3),
			];

			const snapshotItems = [
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440301', 'level3-1', 1, 100), // subtotal: 100
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440302', 'level3-2', 2, 150), // subtotal: 300
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440303', 'level2-2', 1, 200), // subtotal: 200
			];

			const hierarchy = createSnapshotBudgetHierarchy(wbsItems, snapshotItems);

			// With single root WBS item, should return that item directly (not project budget root)
			expect(hierarchy.id).toBe('root');
			expect(hierarchy.data.code).toBe('1');
			expect(hierarchy.data.description).toBe('Root');
			expect(hierarchy.value).toBe(600); // 100 + 300 + 200
			expect(hierarchy.children).toHaveLength(2);

			const level2_1 = hierarchy.children!.find((c) => c.id === 'level2-1');
			const level2_2 = hierarchy.children!.find((c) => c.id === 'level2-2');

			expect(level2_1).toBeDefined();
			expect(level2_1!.value).toBe(400); // 100 + 300
			expect(level2_1!.children).toHaveLength(2);

			expect(level2_2).toBeDefined();
			expect(level2_2!.value).toBe(200);
			expect(level2_2!.children || []).toHaveLength(0);
		});

		it('handles items without snapshot data', () => {
			const wbsItems = [
				createWbsItem('parent-1', null, '1', 'Parent Item', 1),
				createWbsItem('child-1', 'parent-1', '1.1', 'Child with data', 2),
				createWbsItem('child-2', 'parent-1', '1.2', 'Child without data', 2),
			];

			const snapshotItems = [
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440401', 'child-1', 3, 100), // subtotal: 300
			];

			const hierarchy = createSnapshotBudgetHierarchy(wbsItems, snapshotItems);

			// With single root WBS item, should return that item directly (not project budget root)
			expect(hierarchy.id).toBe('parent-1');
			expect(hierarchy.data.code).toBe('1');
			expect(hierarchy.data.description).toBe('Parent Item');
			expect(hierarchy.value).toBe(300); // Only from child-1
			expect(hierarchy.children).toHaveLength(2);

			const childWithData = hierarchy.children!.find((c) => c.id === 'child-1');
			const childWithoutData = hierarchy.children!.find((c) => c.id === 'child-2');

			expect(childWithData!.value).toBe(300);
			expect(childWithData!.data.budgetData).toBeDefined();

			expect(childWithoutData!.value).toBe(0);
			expect(childWithoutData!.data.budgetData).toBeUndefined();
		});

		it('handles multiple root nodes', () => {
			const wbsItems = [
				createWbsItem('root-1', null, '1', 'Root 1', 1),
				createWbsItem('root-2', null, '2', 'Root 2', 1),
				createWbsItem('child-1', 'root-1', '1.1', 'Child 1', 2),
			];

			const snapshotItems = [
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440501', 'child-1', 2, 50), // subtotal: 100
				createSnapshotItem('550e8400-e29b-41d4-a716-446655440502', 'root-2', 1, 300), // subtotal: 300
			];

			const hierarchy = createSnapshotBudgetHierarchy(wbsItems, snapshotItems);

			expect(hierarchy.id).toBe('__project_budget_root__');
			expect(hierarchy.value).toBe(400); // 100 + 300
			expect(hierarchy.children).toHaveLength(2);

			const projectRoot = hierarchy;

			const root1 = projectRoot.children!.find((r) => r.id === 'root-1');
			const root2 = projectRoot.children!.find((r) => r.id === 'root-2');

			expect(root1).toBeDefined();
			expect(root1!.value).toBe(100);
			expect(root1!.children).toHaveLength(1);

			expect(root2).toBeDefined();
			expect(root2!.value).toBe(300);
			expect(root2!.children || []).toHaveLength(0);
		});
	});

	describe('createCurrentBudgetHierarchy', () => {
		it('creates a hierarchy with current budget line items', () => {
			const wbsItems = [
				createWbsItem('parent-1', null, '1', 'Parent Item', 1),
				createWbsItem('child-1', 'parent-1', '1.1', 'Child Item', 2),
			];

			const currentItems = [
				createCurrentBudgetItem('project-1', 'child-1', 3, 75, 1.2), // subtotal: 270
			];

			const hierarchy = createCurrentBudgetHierarchy(wbsItems, currentItems);

			// With single root WBS item, should return that item directly (not project budget root)
			expect(hierarchy.id).toBe('parent-1');
			expect(hierarchy.data.code).toBe('1');
			expect(hierarchy.data.description).toBe('Parent Item');
			expect(hierarchy.value).toBe(270); // Should roll up all child values
			expect(hierarchy.children).toHaveLength(1);

			const child = hierarchy.children![0];
			expect(child.id).toBe('child-1');
			expect(child.data.code).toBe('1.1');
			expect(child.value).toBe(270); // 3 * 75 * 1.2
			expect(child.data.subtotal).toBe(270);
			expect(child.data.budgetData).toBeDefined();
			expect(child.data.budgetData?.quantity).toBe(3);
			expect(child.data.budgetData?.unit_rate).toBe(75);
			expect(child.data.budgetData?.factor).toBe(1.2);
		});

		it('preserves original subtotal while applying cascading factors for hierarchy rollup', () => {
			const wbsItems = [
				createWbsItem('parent-1', null, '1', 'Parent Item', 1),
				createWbsItem('category-1', 'parent-1', '1.1', 'Category with Factor', 2),
				createWbsItem('detail-1', 'category-1', '1.1.1', 'Detail Item', 3),
			];

			const currentItems = [
				// Category has factor 1.5, detail item has factor 1.2
				createCurrentBudgetItem('project-1', 'category-1', 0, 0, 1.5), // Category item (no quantity/rate)
				createCurrentBudgetItem('project-1', 'detail-1', 10, 100, 1.2), // Detail: 10 * 100 * 1.2 = 1200
			];

			const hierarchy = createCurrentBudgetHierarchy(wbsItems, currentItems);

			// Find the detail item
			const category = hierarchy.children![0];
			const detail = category.children![0];

			// Original subtotal should be quantity * unit_rate * original_factor (no cascading)
			expect(detail.data.subtotal).toBe(1200); // 10 * 100 * 1.2 (original factor only)
			expect(detail.data.budgetData?.factor).toBe(1.2); // Original factor preserved

			// Hierarchy value should use cascading factors: 10 * 100 * (1.5 * 1.2) = 1800
			expect(detail.value).toBeCloseTo(1800, 2); // Base value (1000) * totalFactor (1.8)
			expect(category.value).toBeCloseTo(1800, 2); // Should roll up from detail
			expect(hierarchy.value).toBeCloseTo(1800, 2); // Should roll up to parent
		});

		it('demonstrates clear separation between original subtotal and cascading hierarchy values', () => {
			const wbsItems = [
				createWbsItem('root', null, '1', 'Root', 1),
				createWbsItem('cat1', 'root', '1.1', 'Category 1 (Factor 2.0)', 2),
				createWbsItem('cat2', 'cat1', '1.1.1', 'Category 2 (Factor 1.5)', 3),
				createWbsItem('detail', 'cat2', '1.1.1.1', 'Detail Item', 4),
			];

			const currentItems = [
				createCurrentBudgetItem('project-1', 'cat1', 0, 0, 2.0), // Category factor
				createCurrentBudgetItem('project-1', 'cat2', 0, 0, 1.5), // Category factor
				createCurrentBudgetItem('project-1', 'detail', 5, 20, 1.1), // Detail: 5 * 20 * 1.1 = 110
			];

			const hierarchy = createCurrentBudgetHierarchy(wbsItems, currentItems);

			// Navigate to detail item
			const cat1 = hierarchy.children![0];
			const cat2 = cat1.children![0];
			const detail = cat2.children![0];

			// Original subtotal: quantity * unit_rate * item's own factor
			expect(detail.data.subtotal).toBeCloseTo(110, 2); // 5 * 20 * 1.1 (original calculation)
			expect(detail.data.budgetData?.factor).toBe(1.1); // Original factor preserved

			// Hierarchy value: base value * cascading factors (2.0 * 1.5 * 1.1 = 3.3)
			const expectedHierarchyValue = 5 * 20 * 3.3; // 330
			expect(detail.value).toBeCloseTo(expectedHierarchyValue, 2);
			expect(cat2.value).toBeCloseTo(expectedHierarchyValue, 2);
			expect(cat1.value).toBeCloseTo(expectedHierarchyValue, 2);
			expect(hierarchy.value).toBeCloseTo(expectedHierarchyValue, 2);

			// Verify the totalFactor calculation
			expect(detail.data.totalFactor).toBeCloseTo(3.3, 2); // 2.0 * 1.5 * 1.1
		});

		it('correctly calculates subtotal for category items (sum of children × own factor)', () => {
			const wbsItems = [
				createWbsItem('parent', null, '7', 'Parent with Factor', 1),
				createWbsItem('category', 'parent', '7.1', 'Category (no factor)', 2),
				createWbsItem('detail1', 'category', '7.1.1', 'Detail 1', 3),
				createWbsItem('detail2', 'category', '7.1.2', 'Detail 2', 4),
			];

			const currentItems = [
				createCurrentBudgetItem('project-1', 'parent', 0, 0, 1.12), // Parent factor
				// Category has no budget data (no factor)
				createCurrentBudgetItem('project-1', 'detail1', 30, 145000, 0.75), // 30 * 145000 * 0.75 = 3,262,500
				createCurrentBudgetItem('project-1', 'detail2', 30, 160000, 0.2), // 30 * 160000 * 0.2 = 960,000
			];

			const hierarchy = createCurrentBudgetHierarchy(wbsItems, currentItems);

			// Navigate to category item
			const category = hierarchy.children![0];

			// Category subtotal should be sum of children (no own factor applied since it's null/undefined)
			const expectedCategorySubtotal = 3262500 + 960000; // 4,222,500
			expect(category.data.subtotal).toBeCloseTo(expectedCategorySubtotal, 2);

			// Category value should still use cascading factors for hierarchy rollup
			const expectedCategoryValue = expectedCategorySubtotal * 1.12; // 4,729,200
			expect(category.value).toBeCloseTo(expectedCategoryValue, 2);

			// Verify children have correct individual subtotals
			expect(category.children![0].data.subtotal).toBeCloseTo(3262500, 2);
			expect(category.children![1].data.subtotal).toBeCloseTo(960000, 2);
		});
	});

	describe('createBudgetDataForDepth', () => {
		it('filters items at specific depth levels correctly', () => {
			const wbsItems = [
				createWbsItem('root', null, '1', 'Root', 1),
				createWbsItem('level2-1', 'root', '1.1', 'Level 2 Item 1', 2),
				createWbsItem('level2-2', 'root', '1.2', 'Level 2 Item 2', 2),
				createWbsItem('level3-1', 'level2-1', '1.1.1', 'Level 3 Item 1', 3),
				createWbsItem('level3-2', 'level2-1', '1.1.2', 'Level 3 Item 2', 3),
			];

			const currentItems = [
				createCurrentBudgetItem('project-1', 'level3-1', 1, 100, 1), // 100
				createCurrentBudgetItem('project-1', 'level3-2', 2, 150, 1), // 300
				createCurrentBudgetItem('project-1', 'level2-2', 1, 200, 1), // 200
			];

			// Test depth 1 (root level)
			const depth1Results = createBudgetDataForDepth(wbsItems, currentItems, 1, 'test-label');
			expect(depth1Results).toHaveLength(1);
			expect(depth1Results[0]).toEqual({
				wbsCode: '1',
				wbsDescription: 'Root',
				label: 'test-label',
				totalValue: 600, // 100 + 300 + 200
			});

			// Test depth 2 (level 2 items)
			const depth2Results = createBudgetDataForDepth(wbsItems, currentItems, 2, 'test-label');
			expect(depth2Results).toHaveLength(2);

			const level2_1 = depth2Results.find((r) => r.wbsCode === '1.1');
			const level2_2 = depth2Results.find((r) => r.wbsCode === '1.2');

			expect(level2_1).toEqual({
				wbsCode: '1.1',
				wbsDescription: 'Level 2 Item 1',
				label: 'test-label',
				totalValue: 400, // 100 + 300
			});

			expect(level2_2).toEqual({
				wbsCode: '1.2',
				wbsDescription: 'Level 2 Item 2',
				label: 'test-label',
				totalValue: 200,
			});

			// Test depth 3 (level 3 items + fallback from level 2)
			const depth3Results = createBudgetDataForDepth(wbsItems, currentItems, 3, 'test-label');

			expect(depth3Results).toHaveLength(3); // Now includes fallback from level 2

			const level3_1 = depth3Results.find((r) => r.wbsCode === '1.1.1');
			const level3_2 = depth3Results.find((r) => r.wbsCode === '1.1.2');
			const level2_fallback = depth3Results.find((r) => r.wbsCode === '1.2'); // Fallback from level 2

			expect(level3_1).toEqual({
				wbsCode: '1.1.1',
				wbsDescription: 'Level 3 Item 1',
				label: 'test-label',
				totalValue: 100,
			});

			expect(level3_2).toEqual({
				wbsCode: '1.1.2',
				wbsDescription: 'Level 3 Item 2',
				label: 'test-label',
				totalValue: 300,
			});

			// This item is included as fallback because there's no level 3 data under the 1.2 branch
			expect(level2_fallback).toEqual({
				wbsCode: '1.2',
				wbsDescription: 'Level 2 Item 2',
				label: 'test-label',
				totalValue: 200,
			});
		});

		it('uses fallback mechanism when target depth has no items', () => {
			const wbsItems = [
				createWbsItem('root', null, '1', 'Root', 1),
				createWbsItem('level2-1', 'root', '1.1', 'Level 2 Item 1', 2),
			];

			const currentItems = [createCurrentBudgetItem('project-1', 'level2-1', 1, 100, 1)];

			// Test depth 4 (no items at this level, should fallback to nearest parent with data)
			const depth4Results = createBudgetDataForDepth(wbsItems, currentItems, 4, 'test-label');

			expect(depth4Results).toHaveLength(1); // Should include fallback
			expect(depth4Results[0]).toEqual({
				wbsCode: '1.1',
				wbsDescription: 'Level 2 Item 1',
				label: 'test-label',
				totalValue: 100,
			});
		});

		it('handles items with factors correctly', () => {
			const wbsItems = [
				createWbsItem('root', null, '1', 'Root', 1),
				createWbsItem('category', 'root', '1.1', 'Category with Factor', 2),
				createWbsItem('detail', 'category', '1.1.1', 'Detail Item', 3),
			];

			const currentItems = [
				createCurrentBudgetItem('project-1', 'category', 0, 0, 1.5), // Category factor
				createCurrentBudgetItem('project-1', 'detail', 10, 100, 1.2), // Detail: 10 * 100 * 1.2 = 1200
			];

			// Test depth 2 - should include cascading factor calculation
			const depth2Results = createBudgetDataForDepth(wbsItems, currentItems, 2, 'test-label');
			expect(depth2Results).toHaveLength(1);
			expect(depth2Results[0].wbsCode).toBe('1.1');
			expect(depth2Results[0].wbsDescription).toBe('Category with Factor');
			expect(depth2Results[0].totalValue).toBeCloseTo(1800, 2); // 10 * 100 * (1.5 * 1.2) = 1800

			// Test depth 3 - should also include cascading factor calculation
			const depth3Results = createBudgetDataForDepth(wbsItems, currentItems, 3, 'test-label');
			expect(depth3Results).toHaveLength(1);
			expect(depth3Results[0].wbsCode).toBe('1.1.1');
			expect(depth3Results[0].wbsDescription).toBe('Detail Item');
			expect(depth3Results[0].totalValue).toBeCloseTo(1800, 2); // Same value due to cascading factors
		});
	});
});
