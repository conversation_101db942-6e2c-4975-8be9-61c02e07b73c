-- Invoice Table Schema
-- Contains invoice information with project-level access control
-- Invoices are associated with projects through purchase orders (vendor info comes from purchase order)
CREATE TABLE IF NOT EXISTS "public"."invoice" (
	"invoice_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"purchase_order_id" "uuid" NOT NULL,
	"description" "text",
	"invoice_date" "date" NOT NULL,
	"account" "text" NOT NULL,
	"amount" numeric(15, 2) NOT NULL,
	"period" "text", -- the accounting period in YYYY-MM format (e.g., '2024-01' for January 2024)
	"post_date" "date" NOT NULL,
	"notes" "text",
	-- Standard audit fields
	"created_by_user_id" "uuid" DEFAULT "auth"."uid" () NOT NULL,
	"created_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL,
	"updated_at" timestamp with time zone DEFAULT "timezone" ('utc'::"text", "now" ()) NOT NULL
);

ALTER TABLE "public"."invoice" OWNER TO "postgres";

COMMENT ON TABLE "public"."invoice" IS 'Invoices for project expenses and vendor payments';

COMMENT ON COLUMN "public"."invoice"."purchase_order_id" IS 'Purchase order ID associated with the invoice';

COMMENT ON COLUMN "public"."invoice"."invoice_date" IS 'Date the invoice was issued';

COMMENT ON COLUMN "public"."invoice"."account" IS 'Account code for expense categorization';

COMMENT ON COLUMN "public"."invoice"."amount" IS 'Invoice amount';

COMMENT ON COLUMN "public"."invoice"."period" IS 'Accounting period (month) for the invoice';

COMMENT ON COLUMN "public"."invoice"."post_date" IS 'Date the invoice was posted to accounting';

-- Primary key constraint
ALTER TABLE ONLY "public"."invoice"
ADD CONSTRAINT "invoice_pkey" PRIMARY KEY ("invoice_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."invoice"
ADD CONSTRAINT "invoice_purchase_order_id_fkey" FOREIGN KEY ("purchase_order_id") REFERENCES "public"."purchase_order" ("purchase_order_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE ONLY "public"."invoice"
ADD CONSTRAINT "invoice_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Check constraints for data validation
ALTER TABLE ONLY "public"."invoice"
ADD CONSTRAINT "invoice_period_format_check" CHECK (
	"period" IS NULL
	OR "period" ~ '^\d{4}-\d{2}$'
);

-- Indexes for performance
CREATE INDEX "invoice_purchase_order_id_idx" ON "public"."invoice" USING "btree" ("purchase_order_id");

CREATE INDEX "invoice_invoice_date_idx" ON "public"."invoice" USING "btree" ("invoice_date");

CREATE INDEX "invoice_post_date_idx" ON "public"."invoice" USING "btree" ("post_date");

CREATE INDEX "invoice_account_idx" ON "public"."invoice" USING "btree" ("account");

CREATE INDEX "invoice_period_idx" ON "public"."invoice" USING "btree" ("period");

CREATE INDEX "invoice_created_by_user_id_idx" ON "public"."invoice" USING "btree" ("created_by_user_id");

-- Enable Row Level Security
ALTER TABLE "public"."invoice" ENABLE ROW LEVEL SECURITY;

-- Triggers for updated_at
CREATE OR REPLACE TRIGGER "update_updated_at" BEFORE
UPDATE ON "public"."invoice" FOR EACH ROW
EXECUTE FUNCTION "public"."update_updated_at_column" ();

-- Audit trigger for invoice table (function defined in shared/audit_functions.sql)
CREATE OR REPLACE TRIGGER "audit_invoice_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."invoice" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_invoice_changes" ();

-- Row Level Security Policies
-- SELECT policies - users can view invoices for projects they have access to (via purchase order)
CREATE POLICY "Users can view invoices for accessible projects" ON "public"."invoice" FOR
SELECT
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."purchase_order" po
			WHERE
				po."purchase_order_id" = "public"."invoice"."purchase_order_id"
				AND "public"."current_user_has_entity_access" (
					'project'::"public"."entity_type",
					po."project_id"
				)
		)
	);

-- INSERT policies - users can create invoices for projects they have editor access to (via purchase order)
CREATE POLICY "Users can create invoices for projects they can edit" ON "public"."invoice" FOR INSERT TO "authenticated"
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."purchase_order" po
			WHERE
				po."purchase_order_id" = "public"."invoice"."purchase_order_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					po."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- UPDATE policies - users can update invoices for projects they have editor access to (via purchase order)
CREATE POLICY "Users can update invoices for projects they can edit" ON "public"."invoice"
FOR UPDATE
	TO "authenticated" USING (
		EXISTS (
			SELECT
				1
			FROM
				"public"."purchase_order" po
			WHERE
				po."purchase_order_id" = "public"."invoice"."purchase_order_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					po."project_id",
					'editor'::"public"."membership_role"
				)
		)
	)
WITH
	CHECK (
		EXISTS (
			SELECT
				1
			FROM
				"public"."purchase_order" po
			WHERE
				po."purchase_order_id" = "public"."invoice"."purchase_order_id"
				AND "public"."current_user_has_entity_role" (
					'project'::"public"."entity_type",
					po."project_id",
					'editor'::"public"."membership_role"
				)
		)
	);

-- DELETE policies - users can delete invoices for projects they have editor access to (via purchase order)
CREATE POLICY "Users can delete invoices for projects they can edit" ON "public"."invoice" FOR DELETE TO "authenticated" USING (
	EXISTS (
		SELECT
			1
		FROM
			"public"."purchase_order" po
		WHERE
			po."purchase_order_id" = "public"."invoice"."purchase_order_id"
			AND "public"."current_user_has_entity_role" (
				'project'::"public"."entity_type",
				po."project_id",
				'editor'::"public"."membership_role"
			)
	)
);

-- Table-specific functions
-- Function to get invoices accessible to a user for a specific project
CREATE OR REPLACE FUNCTION "public"."get_accessible_invoices" ("project_id_param" "uuid") RETURNS TABLE (
	"invoice_id" "uuid",
	"purchase_order_id" "uuid",
	"po_number" "text",
	"description" "text",
	"invoice_date" "date",
	"vendor_name" "text",
	"account" "text",
	"amount" numeric(15, 2),
	"period" "text",
	"post_date" "date",
	"notes" "text",
	"created_at" timestamp with time zone,
	"created_by_name" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_user_id UUID;
BEGIN
	-- Get the current user ID
	v_user_id := auth.uid();

	-- Check if user has access to the project
	IF NOT public.current_user_has_entity_access('project'::public.entity_type, project_id_param) THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Return invoices for the project with vendor, purchase order, and creator information
	RETURN QUERY
	SELECT
		i.invoice_id,
		i.purchase_order_id,
		po.po_number,
		i.description,
		i.invoice_date,
		v.name AS vendor_name,
		i.account,
		i.amount,
		i.period,
		i.post_date,
		i.notes,
		i.created_at,
		p.full_name AS created_by_name
	FROM public.invoice i
	INNER JOIN public.purchase_order po ON i.purchase_order_id = po.purchase_order_id
	LEFT JOIN public.vendor v ON po.vendor_id = v.vendor_id
	LEFT JOIN public.profile p ON i.created_by_user_id = p.user_id
	WHERE po.project_id = project_id_param
	ORDER BY i.invoice_date DESC, po.po_number;
END;
$$;

ALTER FUNCTION "public"."get_accessible_invoices" ("uuid") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_accessible_invoices" ("uuid") IS 'Get invoices accessible to the current user for a specific project';
